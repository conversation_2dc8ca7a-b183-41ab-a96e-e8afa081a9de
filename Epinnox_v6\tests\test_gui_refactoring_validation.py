#!/usr/bin/env python3
"""
GUI Refactoring Validation Test
Validates that all functionality from Priority 1-7 is preserved after GUI refactoring
"""

import sys
import os
import pytest
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Ensure QApplication exists for testing
if not QApplication.instance():
    app = QApplication(sys.argv)
else:
    app = QApplication.instance()

class TestGUIRefactoringValidation:
    """
    Validation test suite for GUI refactoring
    Ensures all Priority 1-7 functionality is preserved
    """
    
    @classmethod
    def setup_class(cls):
        """Setup test environment"""
        try:
            # Import main interface
            from launch_epinnox import EpinnoxTradingInterface
            
            # Create main interface
            cls.main_interface = EpinnoxTradingInterface()
            
            # Wait for initialization
            QTest.qWait(2000)
            
            print("✅ Main interface initialized for testing")
            
        except Exception as e:
            print(f"❌ Failed to setup test environment: {e}")
            raise
    
    @classmethod
    def teardown_class(cls):
        """Cleanup test environment"""
        try:
            if hasattr(cls, 'main_interface'):
                # Close manual trading window if open
                if hasattr(cls.main_interface, 'manual_trading_window') and cls.main_interface.manual_trading_window:
                    cls.main_interface.manual_trading_window.close()
                
                # Close main interface
                cls.main_interface.close()
                
            print("✅ Test environment cleaned up")
            
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
    
    def test_priority_1_trading_methods_exist(self):
        """Test that all Priority 1 trading methods still exist and are callable"""
        print("🧪 Testing Priority 1: Trading Methods Existence...")
        
        required_methods = [
            'place_limit_buy', 'place_market_buy', 'place_limit_sell',
            'place_market_sell', 'place_limit_close', 'place_market_close',
            'close_all_positions', 'cancel_all_orders'
        ]
        
        for method_name in required_methods:
            assert hasattr(self.main_interface, method_name), f"Method {method_name} missing from main interface"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"Method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        print("✅ Priority 1 trading methods validation PASSED")
    
    def test_priority_1_refresh_methods_exist(self):
        """Test that all Priority 1 refresh methods still exist"""
        print("🧪 Testing Priority 1: Refresh Methods Existence...")
        
        required_methods = [
            'refresh_positions', 'refresh_orders', 'refresh_balance',
            'refresh_market_data', 'refresh_portfolio_status', 'update_balance_display'
        ]
        
        for method_name in required_methods:
            assert hasattr(self.main_interface, method_name), f"Method {method_name} missing from main interface"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"Method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        print("✅ Priority 1 refresh methods validation PASSED")
    
    def test_manual_trading_window_integration(self):
        """Test that manual trading window can be opened and has all required methods"""
        print("🧪 Testing Manual Trading Window Integration...")
        
        # Test that manual trading window can be opened
        assert hasattr(self.main_interface, 'open_manual_trading_window'), "open_manual_trading_window method missing"
        assert callable(self.main_interface.open_manual_trading_window), "open_manual_trading_window not callable"
        
        # Open manual trading window
        self.main_interface.open_manual_trading_window()
        QTest.qWait(1000)  # Wait for window to open
        
        # Verify window exists
        assert hasattr(self.main_interface, 'manual_trading_window'), "manual_trading_window attribute missing"
        assert self.main_interface.manual_trading_window is not None, "manual_trading_window is None"
        
        manual_window = self.main_interface.manual_trading_window
        
        # Test that manual window has all required trading methods
        required_methods = [
            'place_limit_buy', 'place_market_buy', 'place_limit_sell',
            'place_market_sell', 'place_limit_close', 'place_market_close',
            'close_all_positions', 'cancel_all_orders'
        ]
        
        for method_name in required_methods:
            assert hasattr(manual_window, method_name), f"Manual window missing method: {method_name}"
            method = getattr(manual_window, method_name)
            assert callable(method), f"Manual window method {method_name} is not callable"
            print(f"   ✅ Manual window {method_name}: EXISTS and CALLABLE")
        
        print("✅ Manual trading window integration PASSED")
    
    def test_priority_3_ai_orchestrator_integration(self):
        """Test that AI orchestrator integration is preserved"""
        print("🧪 Testing Priority 3: AI Orchestrator Integration...")
        
        # Test AI orchestrator methods exist
        ai_methods = [
            'execute_llm_decision', 'validate_llm_decision', 'start_autonomous_trading_loop',
            'stop_autonomous_trading_loop', 'execute_autonomous_cycle'
        ]
        
        for method_name in ai_methods:
            assert hasattr(self.main_interface, method_name), f"AI method {method_name} missing"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"AI method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        print("✅ Priority 3 AI orchestrator integration PASSED")
    
    def test_priority_4_risk_management_integration(self):
        """Test that risk management system is preserved"""
        print("🧪 Testing Priority 4: Risk Management Integration...")
        
        # Test risk management methods exist
        risk_methods = [
            'setup_risk_management_system', 'validate_trade_with_risk_management',
            'check_emergency_stop_conditions', 'emergency_close_all_positions',
            'emergency_stop_all_trading'
        ]
        
        for method_name in risk_methods:
            assert hasattr(self.main_interface, method_name), f"Risk method {method_name} missing"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"Risk method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        # Test risk manager exists
        assert hasattr(self.main_interface, 'risk_manager'), "risk_manager attribute missing"
        print(f"   ✅ risk_manager: EXISTS")
        
        print("✅ Priority 4 risk management integration PASSED")
    
    def test_priority_5_error_handling_integration(self):
        """Test that error handling system is preserved"""
        print("🧪 Testing Priority 5: Error Handling Integration...")
        
        # Test error handling methods exist
        error_methods = [
            'setup_error_handling_system', 'handle_component_error',
            'get_system_health_status'
        ]
        
        for method_name in error_methods:
            assert hasattr(self.main_interface, method_name), f"Error handling method {method_name} missing"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"Error handling method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        # Test error handler exists
        assert hasattr(self.main_interface, 'error_handler'), "error_handler attribute missing"
        print(f"   ✅ error_handler: EXISTS")
        
        print("✅ Priority 5 error handling integration PASSED")
    
    def test_priority_6_monitoring_integration(self):
        """Test that monitoring dashboard is preserved"""
        print("🧪 Testing Priority 6: Monitoring Integration...")
        
        # Test monitoring methods exist
        monitoring_methods = [
            'setup_monitoring_dashboard', 'update_monitoring_dashboard',
            'log_trade_to_dashboard', 'add_dashboard_alert',
            'get_performance_metrics', 'export_trading_report'
        ]
        
        for method_name in monitoring_methods:
            assert hasattr(self.main_interface, method_name), f"Monitoring method {method_name} missing"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"Monitoring method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        # Test monitoring dashboard exists
        assert hasattr(self.main_interface, 'monitoring_dashboard'), "monitoring_dashboard attribute missing"
        print(f"   ✅ monitoring_dashboard: EXISTS")
        
        print("✅ Priority 6 monitoring integration PASSED")
    
    def test_emergency_controls_in_main_dashboard(self):
        """Test that emergency controls are accessible from main dashboard"""
        print("🧪 Testing Emergency Controls in Main Dashboard...")
        
        # Test emergency control methods
        emergency_methods = [
            'emergency_close_all_positions', 'emergency_stop_all_trading'
        ]
        
        for method_name in emergency_methods:
            assert hasattr(self.main_interface, method_name), f"Emergency method {method_name} missing"
            method = getattr(self.main_interface, method_name)
            assert callable(method), f"Emergency method {method_name} is not callable"
            print(f"   ✅ {method_name}: EXISTS and CALLABLE")
        
        print("✅ Emergency controls validation PASSED")
    
    def test_data_synchronization_between_windows(self):
        """Test that data synchronization works between main and manual trading windows"""
        print("🧪 Testing Data Synchronization Between Windows...")
        
        # Ensure manual trading window is open
        if not hasattr(self.main_interface, 'manual_trading_window') or self.main_interface.manual_trading_window is None:
            self.main_interface.open_manual_trading_window()
            QTest.qWait(1000)
        
        manual_window = self.main_interface.manual_trading_window
        
        # Test signal connections exist
        assert hasattr(manual_window, 'trade_executed'), "trade_executed signal missing"
        assert hasattr(manual_window, 'position_closed'), "position_closed signal missing"
        assert hasattr(manual_window, 'orders_cancelled'), "orders_cancelled signal missing"
        
        # Test signal handler methods exist in main interface
        signal_handlers = [
            'on_manual_trade_executed', 'on_manual_position_closed', 'on_manual_orders_cancelled'
        ]
        
        for handler_name in signal_handlers:
            assert hasattr(self.main_interface, handler_name), f"Signal handler {handler_name} missing"
            handler = getattr(self.main_interface, handler_name)
            assert callable(handler), f"Signal handler {handler_name} is not callable"
            print(f"   ✅ {handler_name}: EXISTS and CALLABLE")
        
        print("✅ Data synchronization validation PASSED")

def run_gui_refactoring_validation():
    """Run all GUI refactoring validation tests"""
    print("🚀 Starting GUI Refactoring Validation Tests...")
    
    # Run pytest on this file
    test_file = __file__
    exit_code = pytest.main([test_file, "-v", "--tb=short"])
    
    return exit_code == 0

if __name__ == "__main__":
    success = run_gui_refactoring_validation()
    sys.exit(0 if success else 1)
