#!/usr/bin/env python3
"""
Comprehensive Configuration Management for Autonomous Trading System
Unified configuration system for all autonomous trading parameters
"""

import os
import json
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class TradingConfig:
    """Core trading configuration"""
    initial_balance: float = 10000.0
    max_positions: int = 3
    min_confidence: float = 0.65
    cycle_delay: int = 30
    use_rl: bool = False
    trading_hours: Dict[str, int] = None
    
    def __post_init__(self):
        if self.trading_hours is None:
            self.trading_hours = {'start': 0, 'end': 24}  # 24/7 by default

@dataclass
class RiskConfig:
    """Risk management configuration"""
    max_daily_loss: float = 0.05  # 5% max daily loss
    max_position_size: float = 0.30  # 30% max per position
    max_leverage: float = 10.0
    max_portfolio_risk: float = 0.20  # 20% max portfolio risk
    stop_loss_pct: float = 0.02  # 2% stop loss
    take_profit_pct: float = 0.04  # 4% take profit

@dataclass
class MLConfig:
    """Machine Learning configuration"""
    model_weights: Dict[str, float] = None
    ensemble_weights: Dict[str, float] = None
    performance_threshold: float = 0.55
    update_frequency: int = 24  # hours
    learning_rate: float = 0.01
    adaptation_window: int = 100
    
    def __post_init__(self):
        if self.model_weights is None:
            self.model_weights = {
                'svm': 1.0,
                'random_forest': 1.0,
                'lstm': 1.0
            }
        if self.ensemble_weights is None:
            self.ensemble_weights = {
                'svm': 0.25,
                'random_forest': 0.25,
                'lstm': 0.25,
                'rsi': 0.05,
                'vwap': 0.05,
                'orderflow': 0.05,
                'volatility': 0.05,
                'sentiment': 0.05
            }

@dataclass
class RLConfig:
    """Reinforcement Learning configuration"""
    model_type: str = 'PPO'
    total_timesteps: int = 100000
    learning_rate: float = 3e-4
    n_steps: int = 2048
    batch_size: int = 64
    n_epochs: int = 10
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_range: float = 0.2
    ent_coef: float = 0.01

@dataclass
class SymbolScannerConfig:
    """Symbol scanner configuration"""
    enabled: bool = True
    mode: str = 'scalping'
    update_interval: float = 5.0
    symbols: list = None
    metrics_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = [
                'BTC/USDT:USDT',
                'ETH/USDT:USDT',
                'DOGE/USDT:USDT',
                'ADA/USDT:USDT',
                'SOL/USDT:USDT'
            ]
        if self.metrics_weights is None:
            self.metrics_weights = {
                'spread_score': 0.25,
                'tick_atr_score': 0.20,
                'flow_score': 0.15,
                'depth_score': 0.20,
                'volume_score': 0.20
            }

@dataclass
class MonitoringConfig:
    """Performance monitoring configuration"""
    db_path: str = 'data/trading_performance.db'
    log_level: str = 'INFO'
    enable_metrics: bool = True
    metrics_interval: int = 60  # seconds
    alert_thresholds: Dict[str, float] = None
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                'daily_loss': -0.05,  # -5%
                'drawdown': -0.10,    # -10%
                'win_rate': 0.40      # 40%
            }

class AutonomousConfigManager:
    """Comprehensive configuration manager for autonomous trading"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or 'configs/autonomous_trading.yaml'
        
        # Initialize default configurations
        self.trading = TradingConfig()
        self.risk = RiskConfig()
        self.ml = MLConfig()
        self.rl = RLConfig()
        self.scanner = SymbolScannerConfig()
        self.monitoring = MonitoringConfig()
        
        # Load configuration if file exists
        if os.path.exists(self.config_file):
            self.load_config()
        else:
            logger.info(f"Config file {self.config_file} not found, using defaults")
    
    def load_config(self):
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r') as f:
                if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)
            
            # Update configurations
            if 'trading' in config_data:
                self.trading = TradingConfig(**config_data['trading'])
            if 'risk' in config_data:
                self.risk = RiskConfig(**config_data['risk'])
            if 'ml' in config_data:
                self.ml = MLConfig(**config_data['ml'])
            if 'rl' in config_data:
                self.rl = RLConfig(**config_data['rl'])
            if 'scanner' in config_data:
                self.scanner = SymbolScannerConfig(**config_data['scanner'])
            if 'monitoring' in config_data:
                self.monitoring = MonitoringConfig(**config_data['monitoring'])
            
            logger.info(f"Configuration loaded from {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            logger.info("Using default configuration")
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            config_data = {
                'trading': asdict(self.trading),
                'risk': asdict(self.risk),
                'ml': asdict(self.ml),
                'rl': asdict(self.rl),
                'scanner': asdict(self.scanner),
                'monitoring': asdict(self.monitoring)
            }
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)
                else:
                    json.dump(config_data, f, indent=2)
            
            logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration as dictionary"""
        return asdict(self.trading)
    
    def get_risk_config(self) -> Dict[str, Any]:
        """Get risk configuration as dictionary"""
        return asdict(self.risk)
    
    def get_ml_config(self) -> Dict[str, Any]:
        """Get ML configuration as dictionary"""
        return asdict(self.ml)
    
    def get_rl_config(self) -> Dict[str, Any]:
        """Get RL configuration as dictionary"""
        return asdict(self.rl)
    
    def get_scanner_config(self) -> Dict[str, Any]:
        """Get scanner configuration as dictionary"""
        return asdict(self.scanner)
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration as dictionary"""
        return asdict(self.monitoring)
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration as dictionary"""
        return {
            'trading': self.get_trading_config(),
            'risk': self.get_risk_config(),
            'ml': self.get_ml_config(),
            'rl': self.get_rl_config(),
            'scanner': self.get_scanner_config(),
            'monitoring': self.get_monitoring_config()
        }
    
    def update_trading_config(self, **kwargs):
        """Update trading configuration"""
        for key, value in kwargs.items():
            if hasattr(self.trading, key):
                setattr(self.trading, key, value)
            else:
                logger.warning(f"Unknown trading config parameter: {key}")
    
    def update_risk_config(self, **kwargs):
        """Update risk configuration"""
        for key, value in kwargs.items():
            if hasattr(self.risk, key):
                setattr(self.risk, key, value)
            else:
                logger.warning(f"Unknown risk config parameter: {key}")
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return validation results"""
        issues = []
        warnings = []
        
        # Validate trading config
        if self.trading.initial_balance <= 0:
            issues.append("Initial balance must be positive")
        if self.trading.max_positions <= 0:
            issues.append("Max positions must be positive")
        if not 0 <= self.trading.min_confidence <= 1:
            issues.append("Min confidence must be between 0 and 1")
        
        # Validate risk config
        if self.risk.max_daily_loss >= 0:
            warnings.append("Max daily loss should be negative (loss limit)")
        if self.risk.max_position_size > 1:
            warnings.append("Max position size > 100% of balance")
        if self.risk.max_leverage > 20:
            warnings.append("Very high leverage (>20x) detected")
        
        # Validate ML config
        if not 0 <= self.ml.performance_threshold <= 1:
            issues.append("Performance threshold must be between 0 and 1")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }
    
    def create_default_config_file(self, filepath: str = None):
        """Create a default configuration file"""
        if filepath is None:
            filepath = self.config_file
        
        # Temporarily set config file path
        original_path = self.config_file
        self.config_file = filepath
        
        # Save default configuration
        self.save_config()
        
        # Restore original path
        self.config_file = original_path
        
        logger.info(f"Default configuration file created at {filepath}")

# Global configuration instance
config_manager = AutonomousConfigManager()

# Create default config file if it doesn't exist
if not os.path.exists(config_manager.config_file):
    config_manager.create_default_config_file()
