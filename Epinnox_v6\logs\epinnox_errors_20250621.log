2025-06-21 12:04:33,108 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x00000159EFB03EE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 12:04:33,110 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x00000159EFB03EE0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 17:15:36,155 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:38,309 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:39,304 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:40,297 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:41,311 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:42,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:43,297 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:44,298 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:45,309 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:46,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:47,535 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:48,302 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:49,671 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:50,299 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:51,303 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:52,296 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:53,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:54,298 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:55,305 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:56,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:57,530 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:58,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:15:59,308 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:00,302 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:01,304 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:02,297 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:03,308 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:04,309 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:05,303 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:06,303 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:07,515 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:08,308 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:09,700 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:10,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:11,307 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:12,310 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:13,301 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:14,312 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:16,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:18,034 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:18,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:19,675 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:20,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:21,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:22,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:23,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:24,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:25,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:26,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:27,854 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:28,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:29,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:30,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:31,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:32,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:33,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:34,312 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:35,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:36,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:37,965 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:56,325 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:58,321 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:16:59,679 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:00,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:01,319 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:02,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:03,325 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:05,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:06,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:07,524 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:08,317 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:09,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:10,319 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:11,320 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:12,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:13,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:14,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:15,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:18,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:19,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:20,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:21,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:22,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:23,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:24,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:25,323 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:26,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:27,496 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:28,318 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:29,322 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:30,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:31,316 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:32,315 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:33,327 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:34,326 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:35,324 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:36,314 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:37,511 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:38,315 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:39,313 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:40,327 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:41,315 - trading.intelligent_limit_order_manager - ERROR - Error checking exchange order status: 'CCXTTradingEngine' object has no attribute 'get_order_status'
2025-06-21 17:17:42,266 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x000001E8B1D32160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 17:17:42,267 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x000001E8B1D32160>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 17:34:27,492 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:34:27,493 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:35:45,634 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:35:45,634 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:37:04,955 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:37:04,956 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:38:23,080 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:38:23,081 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:39:41,452 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:39:41,452 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:40:58,655 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:40:58,656 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:42:17,295 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:42:17,296 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 17:43:36,056 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 17:43:36,056 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 18:56:27,700 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 18:56:27,704 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 18:59:40,283 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 18:59:40,284 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:00:06,101 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:00:06,102 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:01:51,626 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:01:51,627 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:02:10,102 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:02:10,103 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:02:20,561 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:02:20,562 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:02:30,836 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:02:30,848 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:02:44,288 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:02:44,289 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:03:10,481 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:03:10,482 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:04:36,025 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:04:36,027 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:05:01,985 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:05:01,985 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:05:11,116 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:05:11,117 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:05:20,457 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:05:20,458 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:05:35,498 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:05:35,499 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:05:55,661 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:05:55,662 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:06:06,108 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:06:06,109 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:06:09,670 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:06:09,670 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:06:20,773 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:06:20,775 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:06:35,693 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:06:35,695 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:06:56,206 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:06:56,207 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:07:05,673 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:07:05,675 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:07:18,681 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:07:18,682 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:07:25,845 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:07:25,846 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:07:40,071 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:07:40,073 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:08:05,896 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:08:05,897 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:08:25,972 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:08:25,973 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:08:34,958 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:08:34,958 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:08:48,751 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:08:48,752 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:09:06,882 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:09:06,883 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:09:25,280 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:09:25,281 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:09:49,048 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:09:49,048 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:09:56,682 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:09:56,683 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:10:10,504 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:10:10,505 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:10:22,049 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:10:22,050 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:10:36,159 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:10:36,159 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:10:51,041 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:10:51,042 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:11:01,921 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:11:01,921 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:11:11,686 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 19:11:11,686 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 19:37:06,312 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x0000021C4F7144D0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 19:37:06,313 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x0000021C4F7144D0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-06-21 20:56:16,334 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:56:16,335 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:56:43,133 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:56:43,133 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:56:58,087 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:56:58,088 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:57:18,068 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:57:18,068 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:57:43,961 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:57:43,962 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:57:58,765 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:57:58,767 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:58:18,329 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:58:18,330 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:58:43,649 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:58:43,649 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:58:57,537 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:58:57,538 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-06-21 20:59:18,109 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-06-21 20:59:18,110 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
