2025-06-22 07:13:22,792 - main - INFO - Epinnox v6 starting up...
2025-06-22 07:13:22,833 - core.performance_monitor - INFO - Performance monitoring started
2025-06-22 07:13:22,833 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-22 07:13:22,834 - main - INFO - Performance monitoring initialized
2025-06-22 07:13:22,844 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-22 07:13:22,846 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-22 07:13:22,850 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-22 07:13:27,016 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-22 07:13:28,333 - websocket - INFO - Websocket connected
2025-06-22 07:13:30,344 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-22 07:13:30,344 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-22 07:13:30,345 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-22 07:13:30,346 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-22 07:13:30,350 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-22 07:13:32,439 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-22 07:13:32,439 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-22 07:13:32,440 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-22 07:13:32,442 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-06-22 07:13:32,442 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-06-22 07:13:32,443 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 07:13:32,444 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-22 07:13:32,447 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-22 07:13:32,447 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-22 07:13:32,475 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-22 07:13:32,476 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-22 07:13:32,477 - storage.session_manager - INFO - Session Manager initialized
2025-06-22 07:13:32,482 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250622_071332_480b5fc5
2025-06-22 07:13:32,485 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250622_071332_480b5fc5
2025-06-22 07:13:32,555 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 07:14:19,628 - main - INFO - Epinnox v6 starting up...
2025-06-22 07:14:19,644 - core.performance_monitor - INFO - Performance monitoring started
2025-06-22 07:14:19,645 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-22 07:14:19,646 - main - INFO - Performance monitoring initialized
2025-06-22 07:14:19,654 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-22 07:14:19,654 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-22 07:14:19,657 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-22 07:17:43,508 - main - INFO - Epinnox v6 starting up...
2025-06-22 07:17:43,522 - core.performance_monitor - INFO - Performance monitoring started
2025-06-22 07:17:43,522 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-22 07:17:43,523 - main - INFO - Performance monitoring initialized
2025-06-22 07:17:43,533 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-22 07:17:43,533 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-22 07:17:43,536 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-22 07:17:51,975 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-22 07:17:53,184 - websocket - INFO - Websocket connected
2025-06-22 07:17:55,634 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-22 07:17:55,635 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-22 07:17:55,635 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-22 07:17:55,636 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-22 07:17:55,641 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-22 07:17:57,702 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-22 07:17:57,703 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-22 07:17:57,703 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-22 07:17:57,705 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-06-22 07:17:57,705 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-06-22 07:17:57,705 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 07:17:57,706 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-22 07:17:57,708 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-22 07:17:57,708 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-22 07:17:57,724 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-22 07:17:57,725 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-22 07:17:57,726 - storage.session_manager - INFO - Session Manager initialized
2025-06-22 07:17:57,731 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250622_071757_1fb0f822
2025-06-22 07:17:57,734 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250622_071757_1fb0f822
2025-06-22 07:17:57,795 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-22 07:17:57,796 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 07:19:37,900 - main - INFO - Epinnox v6 starting up...
2025-06-22 07:19:37,914 - core.performance_monitor - INFO - Performance monitoring started
2025-06-22 07:19:37,914 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-22 07:19:37,915 - main - INFO - Performance monitoring initialized
2025-06-22 07:19:37,925 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-22 07:19:37,925 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-22 07:19:37,928 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-22 07:19:46,215 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-22 07:19:47,436 - websocket - INFO - Websocket connected
2025-06-22 07:19:49,375 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-22 07:19:49,376 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-22 07:19:49,377 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-22 07:19:49,377 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-22 07:19:49,382 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-22 07:19:51,434 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-22 07:19:51,435 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-22 07:19:51,435 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-22 07:19:51,436 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-06-22 07:19:51,436 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-06-22 07:19:51,437 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 07:19:51,437 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-22 07:19:51,440 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-22 07:19:51,441 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-22 07:19:51,452 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-22 07:19:51,455 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-22 07:19:51,455 - storage.session_manager - INFO - Session Manager initialized
2025-06-22 07:19:51,462 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250622_071951_b7e57d8d
2025-06-22 07:19:51,465 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250622_071951_b7e57d8d
2025-06-22 07:19:51,525 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-22 07:19:51,526 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 07:20:05,583 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:20:05,585 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:20:05,585 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:20:05,587 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:20:05,587 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:20:05,589 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:20:05,589 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:20:10,793 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 806 chars
2025-06-22 07:20:10,794 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that it has been consistently rising without significant support levels or volume indicators to sug...
2025-06-22 07:20:10,796 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 182, Total: 304
2025-06-22 07:20:10,799 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT IT HAS BEEN CONSISTENTLY RISING WITHOUT SIGNIFICANT SUPPORT LEVELS OR VOLUME INDICATORS TO SUGGEST SUSTAINABILITY, WHICH OFTEN PRECEDES CORRECTIONS. GIVEN THIS PATTERN AND CONSIDERING RECENT NEWS ABOUT REGULATORY CHANGES POTENTIALLY IMPACTING INVESTOR SENTIMENT NEGATIVELY, A SHORT POSITION COULD BE PROFITABLE IF THE MARKET CORRECTS AS EXPECTED. THE TAKE-PROFIT LEVEL IS SET AT 10% ABOVE CURRENT PRICE LEVELS TO ENSURE PROFIT IN CASE OF AN UNEXPECTED POSITIVE TURN WHILE MAINTAINING STOP LOSS PROTECTION WITH A THRESHOLD THAT LIMITS POTENTIAL LOSSES SHOULD THE TREND CONTINUE AGAINST OUR PREDICTION.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:20:10,799 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:20:10,857 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:20:10,858 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:20:10,860 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:20:10,861 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155439/$0.155444
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154662
Resistance: $0.156216
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:20:10,861 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:20:17,010 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1035 chars
2025-06-22 07:20:17,011 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is nearing resistance levels while support remains distant at only half of one percent away from curr...
2025-06-22 07:20:17,011 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 236, Total: 761
2025-06-22 07:20:17,014 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEARING RESISTANCE LEVELS WHILE SUPPORT REMAINS DISTANT AT ONLY HALF OF ONE PERCENT AWAY FROM CURRENT PRICES; THIS IMPLIES POTENTIAL DOWNWARD PRESSURE IN THE SHORT TERM. MOMENTUM AND TECHNICAL INDICATORS ARE NEUTRAL BUT CONSIDERING THAT THERE'S AN UNFAVORABLE SPREAD CONDITION (0.100%), IT COULD BE ADVANTAGEOUS TO ENTER A SHORT POSITION NOW, ESPECIALLY WITH FAVORABLE RISK/REWARD RATIO OF 2:1 FOR POTENTIAL PROFIT IF PRICES DROP AS ANTICIPATED NEAR RESISTANCE LEVELS. THE CONFIDENCE LEVEL IS SET AT 75%, ACKNOWLEDGING THE NEUTRALITY IN TECHNICAL INDICATORS BUT LEANING TOWARDS ACTION DUE TO MARKET STRUCTURE AND SPREAD CONDITIONS THAT COULD BENEFIT A SHORT POSITION, WHILE ALSO CONSIDERING WAIT TIME UNTIL VOLUME CONFIRMATION WHICH CAN PROVIDE ADDITIONAL ASSURANCE OF TREND CONTINUANCE OR REVERSAL BEFORE ENTERING WITH LIMIT ORDERS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:20:17,014 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:20:17,015 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:20:17,015 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:20:17,016 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:20:17,838 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155342
2025-06-22 07:20:17,839 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750594817016
2025-06-22 07:20:17,840 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155342
2025-06-22 07:20:17,842 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:20:17,896 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:20:17,898 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:20:17,899 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:20:17,902 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $200.22 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155439 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147667 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147667 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147667 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:20:17,902 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:20:24,863 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1044 chars
2025-06-22 07:20:24,863 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to stable trading conditions for cryptocurrencies like DOGE/USDT that have shown medium setup qu...
2025-06-22 07:20:24,864 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 263, Total: 1179
2025-06-22 07:20:24,865 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO STABLE TRADING CONDITIONS FOR CRYPTOCURRENCIES LIKE DOGE/USDT THAT HAVE SHOWN MEDIUM SETUP QUALITY ACCORDING TO THE OPPORTUNITY CRITERIA PROVIDED IN JSON FORMAT. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO RISK-BASED LIMITS (2% PER TRADE), A LONG POSITION ON DOGE/USDT IS RECOMMENDED WITH AN 85% CONFIDENCE LEVEL, CONSIDERING ITS HIGH LIQUIDITY SPREAD OF LESS THAN 0.2%. THE TAKE PROFIT SHOULD BE SET AT 2%, WHICH ALIGNS WITH THE ACCOUNT'S MAXIMUM ALLOWED EXPOSURE AND RISK BUDGET FOR HEALTH-ADJUSTED TRADING CAPACITY WHILE MAINTAINING A STOP LOSS MARGINALLY HIGHER TO PROTECT AGAINST SUDDEN MARKET MOVEMENTS WITHIN AN ACCEPTABLE RANGE (1.5%). THIS STRATEGY ENSURES THAT WE MAXIMIZE POTENTIAL GAINS WITHOUT COMPROMISING ON SAFETY, GIVEN OUR CURRENT FINANCIAL POSITION OF $200.22 AND THE ACCOUNT'S HEALTH-ADJUSTED RISK PARAMETERS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:20:24,866 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:20:24,924 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:20:24,925 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 19.34s - 3 prompts executed sequentially
2025-06-22 07:21:26,719 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:21:26,720 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:21:26,720 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:21:26,721 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:21:26,722 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:21:26,722 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:21:26,723 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:21:32,420 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 916 chars
2025-06-22 07:21:32,420 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 07:21:32,421 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 209, Total: 331
2025-06-22 07:21:32,422 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET DOES NOT FOLLOW HISTORICAL TRENDS OR IS LESS AFFECTED BY NEW REGULATIONS THAN ANTICIPATED. A STOP LOSS OF 20% WILL PROTECT AGAINST SIGNIFICANT LOSSES IN CASE MY ANALYSIS PROVES INCORRECT AND THE PRICE CONTINUES TO RISE UNEXPECTEDLY, WHICH COULD HAPPEN DUE TO UNFORESEEN POSITIVE NEWS ABOUT BLOCKCHAIN TECHNOLOGY ADVANCEMENTS THAT MIGHT OUTWEIGH REGULATORY CONCERNS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:21:32,422 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:21:32,480 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:21:32,480 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:21:32,481 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:21:32,482 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155325/$0.155326
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154548
Resistance: $0.156102
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:21:32,482 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:21:40,103 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1347 chars
2025-06-22 07:21:40,104 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, despite this balance and favorable spread conditions, there is still an underlying neutral...
2025-06-22 07:21:40,104 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 321, Total: 846
2025-06-22 07:21:40,105 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE. HOWEVER, DESPITE THIS BALANCE AND FAVORABLE SPREAD CONDITIONS, THERE IS STILL AN UNDERLYING NEUTRALITY FROM BOTH ML ENSEMBLE ANALYSIS AND TECHNICAL INDICATORS SUCH AS MOMENTUM WHICH ARE AT ZERO CHANGE (0.00). THE KEY LEVELS SHOW SUPPORT SLIGHTLY BELOW MARKET VALUE ($0.154548) WITH RESISTANCE ABOVE IT ($0.156102), INDICATING A POTENTIAL DOWNWARD TREND IF THE PRICE BREAKS THROUGH THESE POINTS, ESPECIALLY CONSIDERING THAT WE'RE NEARING THIS CRITICAL LEVEL OF $0.154548 FROM CURRENT PRICES WHICH IS ONLY 0.5% AWAY (DISTANCE TO SUPPORT: 0.50%). THE RISK/REWARD RATIO STANDS AT A REASONABLE 2:1 FOR SHORT POSITIONS IN THE MARKET, AND WITH CONFIDENCE SLIGHTLY ABOVE NEUTRAL DUE TO FAVORABLE SPREAD CONDITIONS THAT COULD INDICATE LIQUIDITY ADVANTAGES WHEN ENTERING OR EXITING TRADES QUICKLY IF NEEDED. THEREFORE, IT IS RECOMMENDED TO TAKE A SHORT POSITION NOW ON DOGE/USDT USING A LIMIT ORDER TYPE AT CURRENT PRICE LEVELS WHILE WAITING FOR VOLUME CONFIRMATION AS PER THE ENTRY DECISION JSON DATA PROVIDED WHICH SUGGESTS PATIENCE UNTIL MARKET CONDITIONS ARE MORE FAVORABLE FROM AN ANALYTICAL STANDPOINT BEFORE COMMITTING CAPITAL FULLY INTO THIS TRADE.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:21:40,106 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:21:40,106 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:21:40,107 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:21:40,107 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:21:40,893 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155278
2025-06-22 07:21:40,893 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750594900108
2025-06-22 07:21:40,894 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155278
2025-06-22 07:21:40,895 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:21:40,950 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:21:40,951 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:21:40,951 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:21:40,952 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $192.37 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155325 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147559 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147559 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147559 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:21:40,952 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:21:47,744 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 923 chars
2025-06-22 07:21:47,744 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to stable trading conditions for cryptocurrencies like DOGE/USDT that have shown medium setup qualit...
2025-06-22 07:21:47,745 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 249, Total: 1165
2025-06-22 07:21:47,746 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO STABLE TRADING CONDITIONS FOR CRYPTOCURRENCIES LIKE DOGE/USDT THAT HAVE SHOWN MEDIUM SETUP QUALITY ACCORDING TO THE INTELLIGENT OPPORTUNITY RANKER. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA WITH A RISK BUDGET OF 2% PER TRADE, IT IS ADVISABLE TO ENTER INTO A LONG POSITION ON DOGE/USDT AT $0.155325 USDT PRICE WHILE SETTING TAKE-PROFIT AT DOUBLE THE STOP LOSS (2:1 RATIO) DUE TO HIGH CONFIDENCE IN SETUP QUALITY AND LIQUIDITY CONDITIONS THAT MEET OUR CRITERIA FOR AN EXCELLENT SPREAD OF LESS THAN 0.2%. THE ACCOUNT'S HEALTHY STATUS ALLOWS US A HIGHER RISK BUDGET, BUT WE ARE STILL CONSERVATIVE WITH VOLATILITY PREFERENCES SET AT 1-3%, WHICH ALIGNS WELL WITH THE CURRENT MARKET ENVIRONMENT.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:21:47,746 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:21:47,806 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:21:47,807 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 21.09s - 3 prompts executed sequentially
2025-06-22 07:22:49,572 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:22:49,573 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:22:49,574 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:22:49,577 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:22:49,578 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:22:49,579 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:22:49,580 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:22:54,332 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 624 chars
2025-06-22 07:22:54,333 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that it has been consistently rising, but recent news about regulatory changes and decreasing adopt...
2025-06-22 07:22:54,335 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 162, Total: 284
2025-06-22 07:22:54,336 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT IT HAS BEEN CONSISTENTLY RISING, BUT RECENT NEWS ABOUT REGULATORY CHANGES AND DECREASING ADOPTION RATES SUGGEST AN UPCOMING CORRECTION. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DECLINE WHILE LIMITING POTENTIAL LOSSES IF THE MARKET DOES NOT BEHAVE AS PREDICTED (TAKE-PROFIT AT 10%), OR IN CASE OF FURTHER UNEXPECTED DOWNTURNS BEYOND OUR STOP LOSS THRESHOLD (AT -20%).', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:22:54,337 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:22:54,396 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:22:54,396 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:22:54,397 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:22:54,398 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155464/$0.155465
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154687
Resistance: $0.156241
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:22:54,398 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:23:00,836 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 899 chars
2025-06-22 07:23:00,837 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stable demand and supply. However, the price is near key support levels which could indicate potential downward pressure if confirm...
2025-06-22 07:23:00,837 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 246, Total: 771
2025-06-22 07:23:00,839 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABLE DEMAND AND SUPPLY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF CONFIRMED BY INCREASED TRADING ACTIVITY (VOLUME CONFIRMATION). GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL BUT MOMENTUM HAS NOT SHOWN AN UPWARDS TREND TO JUSTIFY GOING LONG, A SHORT POSITION SEEMS PRUDENT WITH MODERATE CONFIDENCE DUE TO MARKET STABILITY. THE FAVORABLE SPREAD CONDITIONS AND THE RISK/REWARD RATIO OF 2:1 FURTHER SUPPORT THIS DECISION AS IT PROVIDES POTENTIAL FOR PROFIT IF PRICES DROP TOWARDS RESISTANCE LEVELS OR KEY SUPPORTS ARE BREACHED.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:23:00,839 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:23:00,841 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:23:00,841 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:23:00,841 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:23:01,615 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155522
2025-06-22 07:23:01,616 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750594980842
2025-06-22 07:23:01,616 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155522
2025-06-22 07:23:01,617 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:23:01,674 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:23:01,674 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:23:01,675 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:23:01,675 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $184.51 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155464 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147691 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147691 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:23:01,676 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:23:08,511 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 929 chars
2025-06-22 07:23:08,511 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to trading opportunities without excessive risk exposure. Given the account's healthy status and adh...
2025-06-22 07:23:08,512 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 247, Total: 1163
2025-06-22 07:23:08,513 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO TRADING OPPORTUNITIES WITHOUT EXCESSIVE RISK EXPOSURE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA (2.0% MAX RISK PER TRADE), THERE ARE NO IMMEDIATE CONCERNS FOR CAPITAL DEPLETION OR OVEREXPOSURE. THE OPPORTUNITY RANKING SUGGESTS DOGE/USDT AS A HIGH-CONFIDENCE, CONSERVATIVE ENTRY WITH AN ACCEPTABLE 1:3 RISK REWARD RATIO WITHIN THE ACCOUNT'S HEALTHY TRADING PARAMETERS AND LIQUIDITY REQUIREMENTS (<0.2% SPREAD). A TAKE PROFIT OF 2.0% ALIGNS WITH BOTH MARKET VOLATILITY PREFERENCES AND PRESERVATION CRITERIA WHILE A STOP LOSS AT 1.5% PROVIDES AN ADEQUATE SAFETY NET WITHOUT BEING OVERLY CONSERVATIVE, CONSIDERING THE ACCOUNT'S HEALTHY STATUS AND NORMAL TRADING CAPACITY (MAX POSITION SIZE $7.38).", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:23:08,514 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:23:08,578 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:23:08,579 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 19.01s - 3 prompts executed sequentially
2025-06-22 07:24:10,371 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:24:10,371 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:24:10,372 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:24:10,374 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:24:10,374 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:24:10,375 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:24:10,376 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:24:14,625 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 554 chars
2025-06-22 07:24:14,626 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 07:24:14,626 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 140, Total: 262
2025-06-22 07:24:14,628 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. HOWEVER, THERE IS STILL SOME UNCERTAINTY DUE TO RECENT REGULATORY NEWS WHICH COULD EITHER STABILIZE OR FURTHER DEPRESS PRICES; HENCE WE MAINTAIN A MODERATE CONFIDENCE LEVEL AND SET CONSERVATIVE TAKE-PROFIT AND STOP-LOSS LEVELS FOR THIS TRADE.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:24:14,628 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:24:14,685 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:24:14,686 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:24:14,689 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:24:14,689 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155498/$0.155530
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154721
Resistance: $0.156275
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:24:14,691 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:24:20,173 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 819 chars
2025-06-22 07:24:20,174 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near key support levels which could indicate potential downward pressure if confirmed by increased...
2025-06-22 07:24:20,175 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 194, Total: 719
2025-06-22 07:24:20,176 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR KEY SUPPORT LEVELS WHICH COULD INDICATE POTENTIAL DOWNWARD PRESSURE IF CONFIRMED BY INCREASED TRADING ACTIVITY (VOLUME CONFIRMATION). GIVEN THAT MOMENTUM AND ORDERFLOW ARE NEUTRAL BUT THERE'S AN ACCEPTABLE SPREAD CONDITION FAVORABLE FOR ENTRY, IT SEEMS PRUDENT TO INITIATE A SHORT POSITION WITH MODERATE CONFIDENCE DUE TO MARKET STABILITY. THE TAKE-PROFIT IS SET AT 10% ABOVE THE CURRENT PRICE AS WE ANTICIPATE POTENTIAL DOWNWARD MOVEMENT TOWARDS SUPPORT LEVELS WHILE MAINTAINING A STOP LOSS OF 20% BELOW IN CASE OF UNEXPECTED UPWARD MOMENTUM OR FALSE BREAKOUT, ENSURING RISK MANAGEMENT AND CAPITAL PRESERVATION.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:24:20,177 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:24:20,177 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:24:20,178 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:24:20,179 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:24:20,972 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155641
2025-06-22 07:24:20,973 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595060180
2025-06-22 07:24:20,973 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155641
2025-06-22 07:24:20,974 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:24:21,030 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:24:21,030 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:24:21,031 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:24:21,032 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $176.63 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155498 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147723 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147723 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147723 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:24:21,032 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:24:27,799 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 956 chars
2025-06-22 07:24:27,800 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which suggests stable conditions for trading opportunities without excessive risk of sudden price swings. Given the...
2025-06-22 07:24:27,802 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 243, Total: 1159
2025-06-22 07:24:27,805 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH SUGGESTS STABLE CONDITIONS FOR TRADING OPPORTUNITIES WITHOUT EXCESSIVE RISK OF SUDDEN PRICE SWINGS. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ALLOWED TRADING CAPACITY, THERE IS ROOM TO ENTER POSITIONS WHILE ADHERING TO PRESERVATION CRITERIA SUCH AS NOT EXCEEDING 2% MAX RISK PER TRADE OR MORE THAN 70% TOTAL EXPOSURE. THE INTELLIGENT OPPORTUNITY RANKER HIGHLIGHTED DOGE/USDT WITH A CONSERVATIVE ENTRY SETUP AND AN ACCEPTABLE RISK-REWARD RATIO OF 1:3, WHICH ALIGNS WELL WITHIN THE ACCOUNT'S HEALTHY TRADING PARAMETERS. THEREFORE, I RECOMMEND TAKING A LONG POSITION IN DOGE/USDT AT $0.155498 USING HALF OF OUR MAX POSITION SIZE TO MAINTAIN DISCIPLINE WHILE CAPITALIZING ON POTENTIAL GAINS WITHOUT COMPROMISING RISK MANAGEMENT PRINCIPLES SET BY THE PRESERVATION CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:24:27,805 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:24:27,865 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:24:27,866 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.49s - 3 prompts executed sequentially
2025-06-22 07:25:29,643 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:25:29,644 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:25:29,644 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:25:29,645 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:25:29,646 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:25:29,646 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:25:29,647 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:25:34,430 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 593 chars
2025-06-22 07:25:34,431 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a bullish trend with increasing volume and high trading activity. However, the recent spike in price is accompanied by an increase in volatility which suggests that it might be overbought. ...
2025-06-22 07:25:34,431 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 164, Total: 286
2025-06-22 07:25:34,433 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A BULLISH TREND WITH INCREASING VOLUME AND HIGH TRADING ACTIVITY. HOWEVER, THE RECENT SPIKE IN PRICE IS ACCOMPANIED BY AN INCREASE IN VOLATILITY WHICH SUGGESTS THAT IT MIGHT BE OVERBOUGHT. SHORT SELLING COULD CAPITALIZE ON THIS POTENTIAL REVERSAL WHILE LIMITING RISK EXPOSURE THROUGH TAKE-PROFIT AT 10% TO SECURE GAINS IF A DOWNTREND DOES NOT MATERIALIZE, AND SETTING STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF AN UNEXPECTED CONTINUATION OF THE BULLISH TREND.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:25:34,433 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:25:34,490 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:25:34,491 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:25:34,492 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:25:34,493 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155721/$0.155734
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154942
Resistance: $0.156500
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:25:34,493 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:25:41,478 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1164 chars
2025-06-22 07:25:41,478 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is nearing resistance levels while support remains distant at $0.154942 and only slightly above it by...
2025-06-22 07:25:41,479 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 279, Total: 804
2025-06-22 07:25:41,480 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEARING RESISTANCE LEVELS WHILE SUPPORT REMAINS DISTANT AT $0.154942 AND ONLY SLIGHTLY ABOVE IT BY 0.50%. THIS PROXIMITY TO RESISTANCE COULD INDICATE AN IMPENDING DOWNTREND IF NOT CONFIRMED BY INCREASED TRADING ACTIVITY OR MOMENTUM SHIFTS. THE FAVORABLE SPREAD CONDITIONS SUGGEST A COST-EFFECTIVE ENTRY POINT, BUT THE LACK OF VOLUME CONFIRMATION NECESSITATES CAUTION. GIVEN THESE FACTORS AND CONSIDERING THAT TECHNICAL INDICATORS ARE NEUTRAL WITH NO CLEAR TREND DIRECTION FROM MOMENTUM DATA ALONE, I RECOMMEND INITIATING A SHORT POSITION TO CAPITALIZE ON POTENTIAL PRICE DECLINES WHILE SETTING CONSERVATIVE TAKE PROFIT AT 10% ABOVE ENTRY LEVEL DUE TO THE RISK/REWARD RATIO OF 2:1. THE STOP LOSS IS SET HIGHER THAN USUAL (AT 20%) AS AN ADDITIONAL SAFETY MEASURE IN CASE MARKET CONDITIONS CHANGE UNEXPECTEDLY OR IF THERE'S A SUDDEN INCREASE IN VOLUME THAT COULD SIGNAL BUYING PRESSURE AND POTENTIAL PRICE REVERSAL, WHICH WOULD BE DETRIMENTAL TO THE SHORT POSITION.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:25:41,481 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:25:41,481 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:25:41,482 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:25:41,482 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:25:42,313 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155646
2025-06-22 07:25:42,314 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595141483
2025-06-22 07:25:42,315 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155646
2025-06-22 07:25:42,320 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:25:42,374 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:25:42,375 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:25:42,375 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:25:42,382 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $168.73 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155721 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147935 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147935 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147935 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:25:42,382 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:25:48,724 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 915 chars
2025-06-22 07:25:48,724 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which suggests stability in the current trading environment. Given that all symbols have identical price movements ...
2025-06-22 07:25:48,725 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 232, Total: 1148
2025-06-22 07:25:48,725 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH SUGGESTS STABILITY IN THE CURRENT TRADING ENVIRONMENT. GIVEN THAT ALL SYMBOLS HAVE IDENTICAL PRICE MOVEMENTS (+0.0%) BUT DIFFERENT LIQUIDITY (WITH DOGE/USDT HAVING SLIGHTLY BETTER SPREADS), IT IS ADVISABLE TO FOCUS ON THIS PAIR FOR POTENTIAL ENTRY POINTS DUE TO ITS HIGHER SETUP QUALITY AS INDICATED BY JSON DATA, WHICH ALSO ALIGNS WITH THE ACCOUNT'S HEALTHY STATUS AND RISK-ADJUSTED CAPACITY. THE RECOMMENDED TAKE PROFIT OF 2.0% MATCHES OUR MAX TOTAL EXPOSURE LIMIT WHILE ENSURING A STOP LOSS AT 1.5%, PROVIDING AN ADEQUATE SAFETY NET WITHOUT COMPROMISING POTENTIAL GAINS FROM THIS OPPORTUNITY, WHICH IS CONSISTENT WITH THE ACCOUNT'S PRIORITY FOR SURVIVAL OVER PROFITS AND ITS HEALTH-ADJUSTED RISK BUDGET OF 2.0% PER TRADE.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:25:48,726 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:25:48,792 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:25:48,792 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 19.15s - 3 prompts executed sequentially
2025-06-22 07:26:50,588 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:26:50,589 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:26:50,590 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:26:50,591 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:26:50,592 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:26:50,592 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:26:50,593 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:26:54,834 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 489 chars
2025-06-22 07:26:54,835 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within three months, suggesting an impend...
2025-06-22 07:26:54,835 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 129, Total: 251
2025-06-22 07:26:54,836 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN THREE MONTHS, SUGGESTING AN IMPENDING DOWNTURN. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DECLINE WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:26:54,837 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:26:54,897 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:26:54,899 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:26:54,899 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:26:54,902 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155453/$0.155454
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154676
Resistance: $0.156230
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:26:54,902 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:27:00,508 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 780 chars
2025-06-22 07:27:00,509 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, despite this balance and favorable spread conditions, there is still neutrality from techn...
2025-06-22 07:27:00,509 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 195, Total: 720
2025-06-22 07:27:00,510 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE. HOWEVER, DESPITE THIS BALANCE AND FAVORABLE SPREAD CONDITIONS, THERE IS STILL NEUTRALITY FROM TECHNICAL ANALYSIS WHICH DOES NOT STRONGLY INDICATE AN IMMEDIATE DIRECTIONAL MOVE. GIVEN THAT SUPPORT LEVELS ARE CLOSE TO BEING BREACHED AT $0.154676 (WITH A DISTANCE OF 0.50%), THE POTENTIAL FOR DOWNWARD PRESSURE EXISTS BUT ISN'T CONFIRMED BY VOLUME CONFIRMATION YET, HENCE WE OPT FOR SHORT WITH MODERATE CONFIDENCE AND SET TAKE-PROFIT AND STOP LOSS LEVELS ACCORDINGLY TO MANAGE RISK WHILE CAPITALIZING ON POSSIBLE SHORT-TERM PRICE DECLINES IF MOMENTUM SHIFTS NEGATIVELY IN OUR FAVOR.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:27:00,510 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:27:00,511 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:27:00,512 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:27:00,512 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:27:01,349 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155358
2025-06-22 07:27:01,350 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595220512
2025-06-22 07:27:01,350 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155358
2025-06-22 07:27:01,351 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:27:01,409 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:27:01,409 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:27:01,410 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:27:01,412 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $160.71 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155453 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147680 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147680 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147680 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:27:01,412 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:27:08,332 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1084 chars
2025-06-22 07:27:08,334 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which suggests stability in the current environment without significant risk of sudden price swings that could impa...
2025-06-22 07:27:08,335 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 250, Total: 1166
2025-06-22 07:27:08,337 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH SUGGESTS STABILITY IN THE CURRENT ENVIRONMENT WITHOUT SIGNIFICANT RISK OF SUDDEN PRICE SWINGS THAT COULD IMPACT OUR TRADING STRATEGY ADVERSELY. GIVEN THIS CONTEXT ALONG WITH HEALTH-ADJUSTED ACCOUNT CAPACITY PARAMETERS (2.0% PER TRADE), WE CAN CONFIDENTLY CONSIDER A LONG POSITION ON DOGE/USDT AS IT HAS BEEN IDENTIFIED BY INTELLIGENT OPPORTUNITY SCANNING TOOLS TO BE THE BEST OPPORTUNITY, SHOWING CLEAR SETUP QUALITY AND STRONG ALIGNMENT IN MOMENTUM INDICATORS WITHIN OUR RISK TOLERANCE LEVELS. THE TAKE-PROFIT IS SET AT 2.0% OF ACCOUNT BALANCE WHICH ALIGNS WITH MAXIMUM ALLOWED PER TRADE FOR THIS HEALTHY ACCOUNT STATUS WHILE ENSURING WE DO NOT EXCEED TOTAL EXPOSURE LIMITS (70%). A STOP LOSS HAS BEEN DETERMINED TO BE SLIGHTLY HIGHER THAN THE TYPICAL THRESHOLD, REFLECTING A CONSERVATIVE APPROACH DUE TO OUR MODERATE RISK ENVIRONMENT AND AIMED AT PROTECTING AGAINST POTENTIAL DOWNSIDE RISKS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:27:08,337 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:27:08,399 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:27:08,400 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 17.81s - 3 prompts executed sequentially
2025-06-22 07:28:10,183 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:28:10,183 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:28:10,183 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:28:10,185 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:28:10,186 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:28:10,186 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:28:10,187 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:28:14,804 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 602 chars
2025-06-22 07:28:14,805 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an imminent...
2025-06-22 07:28:14,805 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 154, Total: 276
2025-06-22 07:28:14,806 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMMINENT DECLINE. BY TAKING A SHORT POSITION NOW WITH A MODERATE TAKE-PROFIT AT 10% AND SETTING STOP LOSS ORDERS AT 20%, WE CAN CAPITALIZE ON THE EXPECTED DOWNWARD MOVEMENT WHILE LIMITING POTENTIAL LOSSES IF MARKET SENTIMENT SHIFTS UNEXPECTEDLY IN FAVOR OF LONG POSITIONS BEFORE OUR EXIT STRATEGY IS TRIGGERED.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:28:14,807 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:28:14,861 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:28:14,861 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:28:14,864 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:28:14,864 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155126/$0.155127
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154350
Resistance: $0.155902
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:28:14,865 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:28:21,970 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1084 chars
2025-06-22 07:28:21,971 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near resistance levels and has not yet confirmed through increased trading volumes which are pendi...
2025-06-22 07:28:21,971 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 287, Total: 812
2025-06-22 07:28:21,972 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR RESISTANCE LEVELS AND HAS NOT YET CONFIRMED THROUGH INCREASED TRADING VOLUMES WHICH ARE PENDING IN OUR ANALYSIS TIMELINE. GIVEN THAT TECHNICAL INDICATORS LIKE MOMENTUM REMAIN NEUTRAL, WE HAVE ROOM FOR CAUTION BUT ALSO POTENTIAL PROFITABILITY IF PRICES DROP AS ANTICIPATED DUE TO APPROACHING SUPPORT LEVEL PROXIMITY (0.50% DISTANCE). THE FAVORABLE SPREAD CONDITIONS AND A RISK/REWARD RATIO OF 2:1 PROVIDE AN ATTRACTIVE ENTRY POINT WITH MANAGEABLE RISKS THROUGH THE TAKE-PROFIT AT RESISTANCE BREACH, WHILE STOP LOSS IS SET CONSERVATIVELY BELOW CURRENT PRICE LEVELS IN CASE MARKET SENTIMENT SHIFTS UNEXPECTEDLY OR IF THERE'S ANY SUDDEN VOLATILITY.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:28:21,972 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:28:21,973 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:28:21,973 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:28:21,973 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:28:22,789 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155178
2025-06-22 07:28:22,790 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595301975
2025-06-22 07:28:22,791 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155178
2025-06-22 07:28:22,794 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:28:22,847 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:28:22,849 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:28:22,853 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:28:22,854 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $152.38 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155126 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147370 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147370 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147370 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:28:22,855 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:28:30,662 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1269 chars
2025-06-22 07:28:30,663 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to stable trading. Given the account's healthy status and risk budget of 2%, we can consider takin...
2025-06-22 07:28:30,664 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 297, Total: 1213
2025-06-22 07:28:30,667 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO STABLE TRADING. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK BUDGET OF 2%, WE CAN CONSIDER TAKING POSITIONS IN CRYPTOCURRENCIES THAT ALIGN WELL WITH OUR OPPORTUNITY CRITERIA—SPECIFICALLY DOGE/USDT DUE TO ITS HIGH SETUP QUALITY AS INDICATED BY A CLEAR PATTERN OR STRONG SIGNALS, WHICH IS REFLECTED IN AN UPTREND (MOM: +0.0%). THE LIQUIDITY SPREAD FOR THIS PAIRING APPEARS FAVORABLE AT LESS THAN 0.2%, AND THE MOMENTUM ALIGNS WITH OUR CRITERIA OF BEING STRONGLY ALIGNED WITHOUT EXCESSIVE VOLATILITY THAT COULD RISK ACCOUNT HEALTH BEYOND ITS MODERATE THRESHOLD. A CONSERVATIVE RANGE IN PRICE MOVEMENT IS PREFERRED, WHICH FITS WITHIN A NORMAL TO SLIGHTLY HIGHER-THAN-NORMAL TRADING ENVIRONMENT FOR THIS PARTICULAR PAIR DUE TO MARKET CONDITIONS NOT INDICATING EXTREME FLUCTUATIONS OR UNCERTAINTY AT THE MOMENT. THE TAKE PROFIT AND STOP LOSS ARE SET CONSIDERING BOTH POTENTIAL REWARD FROM AN UPTREND CONTINUATION (1:3 RATIO) WHILE ALSO PROTECTING AGAINST SUDDEN ADVERSE PRICE MOVEMENTS, ENSURING THAT OUR ACCOUNT HEALTH REMAINS INTACT WITHIN ITS RISK-ADJUSTED PARAMETERS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:28:30,668 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:28:30,733 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:28:30,734 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 20.55s - 3 prompts executed sequentially
2025-06-22 07:29:32,571 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:29:32,572 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:29:32,573 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:29:32,574 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:29:32,574 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:29:32,575 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:29:32,575 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:29:37,672 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 734 chars
2025-06-22 07:29:37,674 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing volatility. Historical trends show that similar patterns have led to significant price ...
2025-06-22 07:29:37,674 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 181, Total: 303
2025-06-22 07:29:37,677 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING VOLATILITY. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN WEEKS FOLLOWING SUCH PEAKS. BY TAKING A SHORT POSITION NOW, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTREND WHILE SETTING STOP-LOSS ORDERS AT 20% BELOW THE CURRENT MARKET PRICE TO MANAGE RISK EFFECTIVELY. THE TAKE-PROFIT LEVEL IS SET AT 10%, ANTICIPATING THAT IF OUR ANALYSIS PROVES INCORRECT AND PRICES STABILIZE OR INCREASE UNEXPECTEDLY, THERE'S STILL A REASONABLE CHANCE FOR PROFIT BEFORE REACHING BREAK-EVEN POINT.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:29:37,678 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:29:37,742 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:29:37,743 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:29:37,744 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:29:37,747 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155119/$0.155120
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154343
Resistance: $0.155895
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:29:37,748 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:29:44,357 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1077 chars
2025-06-22 07:29:44,358 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is nearing resistance levels which could indicate an upcoming reversal or consolidation phase rather ...
2025-06-22 07:29:44,358 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 250, Total: 775
2025-06-22 07:29:44,360 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEARING RESISTANCE LEVELS WHICH COULD INDICATE AN UPCOMING REVERSAL OR CONSOLIDATION PHASE RATHER THAN IMMEDIATE BREAKOUT POTENTIAL FOR BUYERS. GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL AND MOMENTUM HAS NOT SHOWN SIGNIFICANT MOVEMENT IN EITHER DIRECTION, IT'S PRUDENT TO CONSIDER A SHORT POSITION WITH MODERATE CONFIDENCE DUE TO THESE FACTORS COMBINED WITH THE FAVORABLE SPREAD CONDITIONS WHICH COULD BENEFIT FROM PRICE STABILIZATION OR DECREASE TOWARDS SUPPORT LEVELS BEFORE ANY SUBSTANTIAL UPWARD MOVE OCCURS. THE RISK/REWARD RATIO OF 2:1 IS ACCEPTABLE FOR THIS STRATEGY AS LONG-TERM MARKET SENTIMENT AND OTHER MACROECONOMIC INDICATORS ARE NOT PROVIDED BUT ASSUMED TO BE NEUTRAL, THUS SUPPORTING A CAUTIOUS SHORT POSITION APPROACH WITH DEFINED TAKE PROFIT AT THE RESISTANCE LEVEL MINUS SPREAD COSTS (ASSUMING $0.158395) AND STOP LOSS SET BELOW SUPPORT LEVELS ($0.154343).", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:29:44,360 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:29:44,360 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:29:44,361 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:29:44,362 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:29:45,171 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155079
2025-06-22 07:29:45,172 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595384362
2025-06-22 07:29:45,176 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155079
2025-06-22 07:29:45,177 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:29:45,228 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:29:45,231 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:29:45,235 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:29:45,238 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $143.01 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155119 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147363 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147363 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147363 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:29:45,241 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:29:51,449 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 888 chars
2025-06-22 07:29:51,450 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to stable trading conditions for cryptocurrencies like DOGE/USDT that have shown medium setup qu...
2025-06-22 07:29:51,450 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 220, Total: 1136
2025-06-22 07:29:51,451 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO STABLE TRADING CONDITIONS FOR CRYPTOCURRENCIES LIKE DOGE/USDT THAT HAVE SHOWN MEDIUM SETUP QUALITY IN THE ANALYSIS DATA PROVIDED. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA (MAX RISK PER TRADE AT 2% OF TOTAL BALANCE), A LONG POSITION ON DOGE/USDT IS RECOMMENDED WITH MODERATE CONFIDENCE DUE TO ITS HIGH-QUALITY SETUPS, STRONG MOMENTUM ALIGNMENT, EXCELLENT LIQUIDITY INDICATED BY LOW SPREAD REQUIREMENTS, AND THE FAVORABLE RISK/REWARD RATIO. THE TAKE PROFIT SHOULD BE ALIGNED WITH ACCOUNT HEALTH AT 2%, WHILE STOP LOSS CAN BE SLIGHTLY HIGHER GIVEN NORMAL VOLATILITY LEVELS BUT STILL WITHIN A CONSERVATIVE RANGE TO PROTECT AGAINST SUDDEN MARKET SHIFTS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:29:51,451 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:29:51,513 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:29:51,513 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 18.94s - 3 prompts executed sequentially
2025-06-22 07:30:53,875 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:30:53,877 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:30:53,877 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:30:53,879 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:30:53,880 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:30:53,881 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:30:53,882 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:30:58,807 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 663 chars
2025-06-22 07:30:58,808 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that similar patterns have led to significant corrections within six months, suggesting an impendin...
2025-06-22 07:30:58,808 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 160, Total: 282
2025-06-22 07:30:58,810 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. HOWEVER, CONSIDERING RECENT REGULATORY NEWS AND INCREASED INSTITUTIONAL INTEREST WHICH COULD STABILIZE PRICES TEMPORARILY, I RECOMMEND TAKING PROFITS AT A 10% GAIN WHILE SETTING THE STOP LOSS ORDER AT 20%. THIS STRATEGY BALANCES POTENTIAL GAINS WITH RISK MANAGEMENT IN ANTICIPATION OF MARKET CORRECTION WITHOUT MISSING OUT ON SHORT-TERM OPPORTUNITIES.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:30:58,811 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:30:58,872 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:30:58,872 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:30:58,873 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:30:58,874 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.155127/$0.155147
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154351
Resistance: $0.155903
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:30:58,874 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:31:05,837 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1238 chars
2025-06-22 07:31:05,837 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 2.0%
STOP_LOSS: 3.5%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price range. Although technical indicators are neutral and momentum is at zero, which typically would no...
2025-06-22 07:31:05,838 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 280, Total: 805
2025-06-22 07:31:05,839 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE RANGE. ALTHOUGH TECHNICAL INDICATORS ARE NEUTRAL AND MOMENTUM IS AT ZERO, WHICH TYPICALLY WOULD NOT PROVIDE STRONG TRADING SIGNALS ON THEIR OWN, WE HAVE FAVORABLE SPREAD CONDITIONS THAT CAN BE ADVANTAGEOUS FOR SHORT SELLING STRATEGIES. THE KEY LEVELS SHOW A SUPPORT LEVEL SLIGHTLY BELOW OUR ENTRY POINT WITH RESISTANCE ABOVE IT; THIS CREATES AN OPPORTUNITY TO ANTICIPATE POTENTIAL PRICE REVERSALS TOWARDS THE DOWNSIDE AS SELLERS MAY PUSH PRICES CLOSER TO OR BEYOND THE $0.154351 SUPPORT THRESHOLD, ESPECIALLY IF VOLUME CONFIRMATION INDICATES INCREASED SELLING PRESSURE IN THAT RANGE. THE RISK/REWARD RATIO OF 2:1 IS ACCEPTABLE FOR A SHORT POSITION GIVEN THESE CONDITIONS AND MARKET SENTIMENT ANALYSIS SUGGESTS CAUTION DUE TO NEUTRALITY ACROSS VARIOUS INDICATORS BUT WITH AN INCLINATION TOWARDS POTENTIAL DOWNSIDE MOVEMENT BASED ON THE CURRENT STRUCTURE, HENCE RECOMMENDING A SHORT ENTRY NOW VIA LIMIT ORDER AT $0.155903 OR WAITING IF VOLUME CONFIRMATION IS NOT YET AVAILABLE FOR BETTER CERTAINTY OF SELLING PRESSURE IN THAT PRICE RANGE.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:31:05,839 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:31:05,840 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:31:05,840 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:31:05,841 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:31:06,608 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.155038
2025-06-22 07:31:06,608 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595465841
2025-06-22 07:31:06,609 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.155038
2025-06-22 07:31:06,611 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:31:06,668 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:31:06,668 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:31:06,669 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:31:06,671 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $134.85 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.155127 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147371 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147371 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147371 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:31:06,671 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:31:13,141 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 956 chars
2025-06-22 07:31:13,142 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to trading opportunities without excessive risk exposure. Given the account's healthy status and...
2025-06-22 07:31:13,142 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 233, Total: 1149
2025-06-22 07:31:13,144 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO TRADING OPPORTUNITIES WITHOUT EXCESSIVE RISK EXPOSURE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ADHERENCE TO PRESERVATION CRITERIA (MAX 2% PER TRADE), THERE ARE NO IMMEDIATE CONCERNS FOR CAPITAL EROSION FROM THIS POSITION SIZE OR TOTAL EXPOSURE LEVEL. THE OPPORTUNITY RANKING HIGHLIGHTS DOGE/USDT AS A HIGH-CONFIDENCE, CONSERVATIVE ENTRY WITH AN ACCEPTABLE RISK REWARD RATIO OF 1:3 AND LIQUIDITY WITHIN THE REQUIRED PARAMETERS (<0.2%). THIS ALIGNS WELL WITH OUR ACCOUNT'S HEALTH STATUS ALLOWING FOR NORMAL TRADING ACTIVITIES WHILE MAINTAINING CAPITAL PRESERVATION PRIORITIES. THE TAKE PROFIT IS SET AT A MODERATE LEVEL TO ENSURE PROFITS ARE REALIZED WITHOUT OVERLY AGGRESSIVE RISK, CONSIDERING BOTH MARKET CONDITIONS AND PERSONAL STRATEGY PREFERENCES.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:31:13,145 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:31:13,208 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:31:13,208 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 19.33s - 3 prompts executed sequentially
2025-06-22 07:32:14,484 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:32:14,485 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:32:14,486 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:32:14,488 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:32:14,489 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:32:14,490 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:32:14,490 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:32:18,960 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 487 chars
2025-06-22 07:32:18,961 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 07:32:18,962 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 129, Total: 251
2025-06-22 07:32:18,963 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DECLINE WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:32:18,964 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:32:19,014 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:32:19,014 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:32:19,015 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:32:19,017 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.154869/$0.154894
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154095
Resistance: $0.155643
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:32:19,017 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:32:23,960 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 760 chars
2025-06-22 07:32:23,960 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is near resistance levels and has not yet confirmed through increased volume which could indicate an ...
2025-06-22 07:32:23,961 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 177, Total: 702
2025-06-22 07:32:23,961 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'ACTION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': 'THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEAR RESISTANCE LEVELS AND HAS NOT YET CONFIRMED THROUGH INCREASED VOLUME WHICH COULD INDICATE AN UPCOMING REVERSAL OR CONSOLIDATION PHASE RATHER THAN IMMEDIATE BREAKOUT POTENTIAL FOR BUYERS. GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL WITHOUT MOMENTUM TO SUGGEST STRONG TREND DIRECTIONALITY, A SHORT POSITION SEEMS PRUDENT WITH MODERATE CONFIDENCE DUE TO THE FAVORABLE SPREAD CONDITIONS AND RISK/REWARD RATIO OF 2:1 WHICH SUPPORTS THIS DECISION DESPITE NOT BEING AT EXTREME LEVELS OR CLEAR SIGNALS FROM OTHER FACTORS LIKE VOLUME CONFIRMATION.'}
2025-06-22 07:32:23,962 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:32:23,963 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:32:23,964 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:32:23,964 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:32:24,761 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.154810
2025-06-22 07:32:24,762 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595543965
2025-06-22 07:32:24,762 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.154810
2025-06-22 07:32:24,764 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:32:24,816 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:32:24,817 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:32:24,817 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:32:24,821 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $124.63 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.154869 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147126 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147126 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147126 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:32:24,821 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:32:30,805 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 942 chars
2025-06-22 07:32:30,806 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to stable trading conditions for cryptocurrencies like DOGE/USDT that have shown medium setup qu...
2025-06-22 07:32:30,807 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 236, Total: 1152
2025-06-22 07:32:30,808 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO STABLE TRADING CONDITIONS FOR CRYPTOCURRENCIES LIKE DOGE/USDT THAT HAVE SHOWN MEDIUM SETUP QUALITY ACCORDING TO THE OPPORTUNITY CRITERIA PROVIDED IN JSON FORMAT. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK BUDGET OF 2% PER TRADE (HEALTH-ADJUSTED), A LONG POSITION ON DOGE/USDT IS RECOMMENDED WITH AN AGGRESSIVE YET CONSERVATIVE TAKE PROFIT AT 2%, SLIGHTLY HIGHER THAN USUAL DUE TO STRONG ALIGNMENT, LIQUIDITY CONDITIONS BEING EXCELLENT AS INDICATED BY THE SPREADS BELOW 0.2%. THE STOP LOSS HAS BEEN SET MARGINALLY ABOVE THIS LEVEL (1.5%) CONSIDERING POTENTIAL MARKET FLUCTUATIONS WITHIN A NORMAL RANGE OF VOLATILITY AND ENSURING ACCOUNT PRESERVATION CRITERIA ARE MET WITHOUT EXCEEDING HEALTH-BASED LIMITS ON EXPOSURE OR RISK PER TRADE.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:32:30,810 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:32:30,861 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:32:30,861 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 16.38s - 3 prompts executed sequentially
2025-06-22 07:32:33,223 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:32:33,223 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:32:33,224 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:32:33,226 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:32:33,226 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:32:33,227 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.154872/$0.154891
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154098
Resistance: $0.155646
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:32:33,227 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:32:39,943 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1047 chars
2025-06-22 07:32:39,943 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the bid/ask spread is slightly wider than usual at 0.100%, which could indicate less liquidity and higher poten...
2025-06-22 07:32:39,943 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 284, Total: 809
2025-06-22 07:32:39,944 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE BID/ASK SPREAD IS SLIGHTLY WIDER THAN USUAL AT 0.100%, WHICH COULD INDICATE LESS LIQUIDITY AND HIGHER POTENTIAL SLIPPAGE FOR TRADES NEAR KEY LEVELS. GIVEN THAT TECHNICAL INDICATORS ARE NEUTRAL BUT THERE'S AN UPWARD MOMENTUM (ALBEIT VERY LOW), A SHORT POSITION ON DOGE/USDT SEEMS PRUDENT, ESPECIALLY CONSIDERING THE FAVORABLE SPREAD CONDITIONS AS THEY MAY ALLOW BETTER ENTRY PRICES OR EXIT POINTS IN CASE OF MARKET MOVEMENT AGAINST OUR DIRECTION. THE RISK-REWARD RATIO IS 2:1 WHICH SUPPORTS TAKING SOME LEVEL OF ACTION RATHER THAN WAITING FOR VOLUME CONFIRMATION ALONE TO MAKE A DECISION.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:32:39,945 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:32:39,945 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:32:39,946 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:32:39,947 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:32:40,733 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.154790
2025-06-22 07:32:40,733 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595559947
2025-06-22 07:32:40,733 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.154790
2025-06-22 07:32:40,734 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:32:40,785 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:32:40,785 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 7.56s - 1 prompts executed sequentially
2025-06-22 07:33:42,559 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:33:42,559 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:33:42,560 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:33:42,560 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:33:42,561 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:33:42,563 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:33:42,564 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:33:47,348 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 599 chars
2025-06-22 07:33:47,349 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that similar patterns have led to significa...
2025-06-22 07:33:47,349 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 153, Total: 275
2025-06-22 07:33:47,350 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SHORT TIME FRAMES. A SHORT POSITION IS RECOMMENDED AS IT CAPITALIZES ON EXPECTED DOWNWARD MOVEMENT WHILE LIMITING RISK EXPOSURE THROUGH A TAKE-PROFIT AT 10% ABOVE THE ENTRY POINT, WITH AN ADDITIONAL STOP LOSS SET AT 20% BELOW TO PROTECT AGAINST FURTHER ADVERSE PRICE MOVEMENTS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:33:47,351 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:33:47,415 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:33:47,415 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:33:47,416 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:33:47,417 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.154780/$0.154807
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.154006
Resistance: $0.155554
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:33:47,417 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:33:55,097 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1358 chars
2025-06-22 07:33:55,098 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability in the current price. However, technical indicators such as Momentum are neutral and do not provide strong bullish or bea...
2025-06-22 07:33:55,098 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 324, Total: 849
2025-06-22 07:33:55,099 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY IN THE CURRENT PRICE. HOWEVER, TECHNICAL INDICATORS SUCH AS MOMENTUM ARE NEUTRAL AND DO NOT PROVIDE STRONG BULLISH OR BEARISH SIGNALS ON THEIR OWN. DESPITE THIS, THERE IS AN L2 IMBALANCE OF 0.0%, WHICH CAN SOMETIMES INDICATE A POTENTIAL SHIFT TOWARDS HIGHER VOLATILITY BUT DOES NOT NECESSARILY PREDICT IMMEDIATE PRICE MOVEMENT IN EITHER DIRECTION. THE KEY LEVELS SHOW SUPPORT AT $0.154006 AND RESISTANCE AT $0.155554, WITH THE CURRENT BID/ASK SPREAD BEING NARROW (0.100%), WHICH IS FAVORABLE FOR A SHORT POSITION AS IT INDICATES LIQUIDITY WITHOUT SIGNIFICANT SLIPPAGE RISK. THE DISTANCE TO SUPPORT FROM THE CURRENT PRICE LEVEL SUGGESTS THAT THERE MIGHT BE SOME DOWNWARD PRESSURE ON PRICES BUT NOT ENOUGH TO TRIGGER AN IMMEDIATE SELL SIGNAL BASED SOLELY ON TECHNICAL ANALYSIS. GIVEN THESE FACTORS, AND CONSIDERING THE MARKET'S NEUTRAL STANCE WITH A SLIGHT LEAN TOWARDS STABILITY DUE TO BALANCED RECENT FLOW WITHOUT VOLUME SPIKES OR SIGNIFICANT MOMENTUM CHANGES, ENTERING NOW AS SHORT SEEMS REASONABLE IF ONE IS WILLING TO MANAGE RISK CAREFULLY THROUGH TAKE-PROFIT AT 10% AND STOP LOSS AT 20%. THE CONFIDENCE LEVEL REFLECTS THE CAUTIOUS OPTIMISM BASED ON CURRENT MARKET CONDITIONS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:33:55,100 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:33:55,101 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:33:55,101 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:33:55,102 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:33:56,370 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.154665
2025-06-22 07:33:56,371 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595635102
2025-06-22 07:33:56,372 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.154665
2025-06-22 07:33:56,373 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:33:56,438 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:33:56,439 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:33:56,441 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:33:56,441 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $107.49 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.154780 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.147041 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.147041 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.147041 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:33:56,442 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:34:02,512 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 737 chars
2025-06-22 07:34:02,514 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to stable trading conditions for cryptocurrencies like DOGE/USDT that have high setup quality as...
2025-06-22 07:34:02,515 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 206, Total: 1122
2025-06-22 07:34:02,518 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO STABLE TRADING CONDITIONS FOR CRYPTOCURRENCIES LIKE DOGE/USDT THAT HAVE HIGH SETUP QUALITY AS PER THE OPPORTUNITY CRITERIA. GIVEN THE ACCOUNT'S HEALTHY STATUS AND RISK BUDGET OF 2% PER TRADE ALIGNS WELL WITH THIS POSITION SIZE LIMIT, A LONG ENTRY INTO DOGE/USDT IS RECOMMENDED AT $0.154780 USDT PRICE FOR AN EXPECTED PROFIT-TO-RISK RATIO THAT EXCEEDS OUR CONSERVATIVE THRESHOLD (>3:1). THE TAKE_PROFIT AND STOP_LOSS ARE SET TO ENSURE THE ACCOUNT'S RISK EXPOSURE REMAINS WITHIN HEALTHY LIMITS WHILE CAPITALIZING ON POTENTIAL GAINS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:34:02,519 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:34:02,586 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:34:02,587 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 20.03s - 3 prompts executed sequentially
2025-06-22 07:35:04,830 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:35:04,831 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:35:04,832 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:35:04,835 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:35:04,835 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:35:04,836 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:35:04,837 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:35:09,610 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 653 chars
2025-06-22 07:35:09,611 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns show that similar valuations have led to significant corrections within six months, suggesting an impe...
2025-06-22 07:35:09,612 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 159, Total: 281
2025-06-22 07:35:09,613 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SHOW THAT SIMILAR VALUATIONS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. HOWEVER, GIVEN RECENT REGULATORY NEWS AND INCREASED INSTITUTIONAL INTEREST WHICH COULD STABILIZE PRICES TEMPORARILY, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK MANAGEMENT STRATEGIES SUCH AS SETTING TAKE-PROFIT AT 10% IN CASE OF POSITIVE MARKET SENTIMENT REVERSAL OR STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:35:09,613 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:35:09,674 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:35:09,675 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:35:09,675 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:35:09,678 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.154689/$0.154690
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.153916
Resistance: $0.155462
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:35:09,679 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:35:15,544 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 944 chars
2025-06-22 07:35:15,545 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. Although the price is near key support levels and momentum remains neutral, there's an opportunity to capitalize on pote...
2025-06-22 07:35:15,545 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 223, Total: 748
2025-06-22 07:35:15,546 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. ALTHOUGH THE PRICE IS NEAR KEY SUPPORT LEVELS AND MOMENTUM REMAINS NEUTRAL, THERE'S AN OPPORTUNITY TO CAPITALIZE ON POTENTIAL DOWNWARD MOVEMENT DUE TO L2 IMBALANCE BEING AT ZERO PERCENT WHICH COULD INDICATE LESS LIQUIDITY IN FAVOR OF BUYERS. THE SPREAD CONDITIONS ARE CURRENTLY FAVOURABLE FOR A SHORT POSITION AS IT IMPLIES LOWER TRANSACTION COSTS WHEN BUYING THE UNDERLYING ASSET (DOGE) AND SELLING USDT, INCREASING PROFITABILITY ON POTENTIAL PRICE DROPS. GIVEN THAT THERE'S NO IMMEDIATE VOLUME CONFIRMATION BUT OTHER FACTORS SUCH AS L2 IMBALANCE POINT TOWARDS LIQUIDITY FAVORING SELLERS IN THIS MARKET PAIR, A SHORT POSITION WITH MODERATE RISK/REWARD RATIO OF 2:1 IS RECOMMENDED TO CAPITALIZE ON THE CURRENT CONDITIONS WHILE WAITING FOR POTENTIAL PRICE MOVEMENT.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:35:15,547 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:35:15,547 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:35:15,549 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:35:15,549 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:35:16,300 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.154692
2025-06-22 07:35:16,301 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595715550
2025-06-22 07:35:16,305 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.154692
2025-06-22 07:35:16,306 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:35:16,366 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:35:16,369 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:35:16,369 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:35:16,370 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $100.02 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.154689 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.146955 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.146955 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.146955 | Mom: +0.0% | Vol: 1.0x | Setup: ME...
2025-06-22 07:35:16,370 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:35:25,458 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1564 chars
2025-06-22 07:35:25,460 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market data indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to trading without excessive risk exposure. Given the account's healthy status and allowed trading c...
2025-06-22 07:35:25,462 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 916, Completion: 373, Total: 1289
2025-06-22 07:35:25,467 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET DATA INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO TRADING WITHOUT EXCESSIVE RISK EXPOSURE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ALLOWED TRADING CONDITIONS, THERE ARE NO IMMEDIATE CONCERNS FOR PRESERVATION CRITERIA VIOLATIONS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:35:25,469 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:35:25,530 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:35:25,531 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 20.70s - 3 prompts executed sequentially
2025-06-22 07:36:28,140 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 07:36:28,141 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 07:36:28,141 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 07:36:28,142 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:36:28,143 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:36:28,143 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 07:36:28,143 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:36:33,007 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 588 chars
2025-06-22 07:36:33,008 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a bullish trend with increasing volume and high trading activity. However, the recent spike in price is accompanied by an increase in volatility which suggests that it might be overbought. ...
2025-06-22 07:36:33,009 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 155, Total: 277
2025-06-22 07:36:33,011 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A BULLISH TREND WITH INCREASING VOLUME AND HIGH TRADING ACTIVITY. HOWEVER, THE RECENT SPIKE IN PRICE IS ACCOMPANIED BY AN INCREASE IN VOLATILITY WHICH SUGGESTS THAT IT MIGHT BE OVERBOUGHT. SHORT SELLING COULD CAPITALIZE ON THIS POTENTIAL REVERSAL WHILE LIMITING RISK EXPOSURE THROUGH TAKE-PROFIT AT 10% GAIN IF PRICES STABILIZE OR DECREASE SLIGHTLY AND STOP LOSS SET TO PROTECT AGAINST A SIGNIFICANT DROP, SHOULD THE BULLISH TREND CONTINUE UNEXPECTEDLY BEYOND OUR EXPECTATIONS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 07:36:33,011 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 07:36:33,069 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 07:36:33,069 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:36:33,069 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:36:33,072 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.154584/$0.154593
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.153811
Resistance: $0.155357
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-06-22 07:36:33,072 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:36:39,760 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1235 chars
2025-06-22 07:36:39,761 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 75%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANATION: The market microstructure indicates a balanced recent flow with no volume spike, suggesting stability. However, the price is nearing resistance levels while support remains distant at only half of one percent away from curr...
2025-06-22 07:36:39,762 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 525, Completion: 263, Total: 788
2025-06-22 07:36:39,765 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'EXPLANATION': "THE MARKET MICROSTRUCTURE INDICATES A BALANCED RECENT FLOW WITH NO VOLUME SPIKE, SUGGESTING STABILITY. HOWEVER, THE PRICE IS NEARING RESISTANCE LEVELS WHILE SUPPORT REMAINS DISTANT AT ONLY HALF OF ONE PERCENT AWAY FROM CURRENT PRICES; THIS COULD INDICATE AN IMPENDING DOWNTREND IF MOMENTUM AND ORDERFLOW ALIGN IN FAVOR OF SELLERS (WHICH THEY CURRENTLY DO NOT). THE SPREAD CONDITIONS ARE FAVORABLE FOR A SHORT POSITION AS IT'S NARROW, INDICATING LIQUIDITY. ALTHOUGH THE SIGNAL ANALYSIS SHOWS NEUTRAL TECHNICAL INDICATORS WITH NO CLEAR TREND DIRECTION FROM VOLUME OR MOMENTUM DATA ALONE, MARKET MICROSTRUCTURE FACTORS LEAN TOWARDS TAKING ADVANTAGE OF POTENTIAL DOWNWARD PRESSURE BEFORE RESISTANCE LEVELS BECOME MORE PRONOUNCED AND POTENTIALLY LEAD TO PRICE DROPS THAT COULD BE PROFITABLE FOR A SHORT POSITION. THE RISK/REWARD RATIO IS ACCEPTABLE AT 2:1 GIVEN THE CONFIDENCE LEVEL IN THESE INDICATORS COMBINED WITH CURRENT SPREAD CONDITIONS FAVORING ENTRY INTO A MARKET SELL-OFF SCENARIO, HENCE RECOMMENDING A SHORT STRATEGY NOW RATHER THAN WAITING FURTHER WHICH MIGHT RESULT IN MISSED OPPORTUNITIES OR INCREASED VOLATILITY THAT COULD AFFECT POSITION STABILITY.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:36:39,766 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-06-22 07:36:39,767 - core.llm_action_executors - INFO - Executing entry action: ENTER_NOW (LIMIT order)
2025-06-22 07:36:39,768 - core.llm_action_executors - INFO - 🎯 Placing intelligent limit order: BUY 10.0 DOGE/USDT:USDT (confidence: 75.0%)
2025-06-22 07:36:39,768 - trading.intelligent_limit_order_manager - INFO - ✅ Cancelled 0 orders (reason: new_order_cleanup)
2025-06-22 07:36:40,573 - trading.intelligent_limit_order_manager - INFO - ✅ Smart limit order placed: BUY 10.0 DOGE/USDT:USDT @ $0.154429
2025-06-22 07:36:40,573 - core.llm_action_executors - INFO - ✅ Intelligent limit order placed: smart_buy_DOGE/USDT:USDT_1750595799769
2025-06-22 07:36:40,573 - core.llm_action_executors - INFO -    📊 BUY 10.0 DOGE/USDT:USDT @ $0.154429
2025-06-22 07:36:40,574 - core.llm_action_executors - INFO -    ⏰ Timeout: 60s | Confidence: 75.0%
2025-06-22 07:36:40,635 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 07:36:40,635 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 07:36:40,636 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 07:36:40,637 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $91.86 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.154584 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $0.146855 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $0.146855 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $0.146855 | Mom: +0.0% | Vol: 1.0x | Setup: MED...
2025-06-22 07:36:40,637 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 07:36:49,333 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1404 chars
2025-06-22 07:36:49,333 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG
CONFIDENCE: 85%
TAKE_PROFIT: 2.0%
STOP_LOSS: 1.5%
EXPLANATION: The market overview indicates a neutral sector momentum with normal volatility and average volume profile, which is conducive to trading without excessive risk exposure. Given the account's healthy status and allowed tradi...
2025-06-22 07:36:49,333 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 914, Completion: 339, Total: 1253
2025-06-22 07:36:49,335 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE MARKET OVERVIEW INDICATES A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE, WHICH IS CONDUCIVE TO TRADING WITHOUT EXCESSIVE RISK EXPOSURE. GIVEN THE ACCOUNT'S HEALTHY STATUS AND ALLOWED TRADING CONDITIONS, WE CAN CONSIDER ENTERING TRADES THAT ALIGN WELL WITHIN OUR PRESERVATION CRITERIA.", 'ACTION': 'ENTER_NOW'}
2025-06-22 07:36:49,335 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-06-22 07:36:49,389 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 07:36:49,390 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 21.25s - 3 prompts executed sequentially
2025-06-22 08:39:39,141 - main - INFO - Epinnox v6 starting up...
2025-06-22 08:39:39,154 - core.performance_monitor - INFO - Performance monitoring started
2025-06-22 08:39:39,155 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-22 08:39:39,155 - main - INFO - Performance monitoring initialized
2025-06-22 08:39:39,156 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-22 08:39:39,159 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-22 08:39:47,901 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-22 08:39:49,096 - websocket - INFO - Websocket connected
2025-06-22 08:39:51,916 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-22 08:39:51,917 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-22 08:39:51,917 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-22 08:39:51,918 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-22 08:39:51,923 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-22 08:39:53,987 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-22 08:39:53,988 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-22 08:39:53,989 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-22 08:39:53,990 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-06-22 08:39:53,990 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-06-22 08:39:53,991 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 08:39:53,992 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-22 08:39:53,992 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-06-22 08:39:53,995 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-22 08:39:53,995 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-22 08:39:54,005 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-22 08:39:54,007 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-22 08:39:54,008 - storage.session_manager - INFO - Session Manager initialized
2025-06-22 08:39:54,013 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250622_083954_5f2874db
2025-06-22 08:39:54,016 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250622_083954_5f2874db
2025-06-22 08:39:54,102 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-06-22 08:39:54,103 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-06-22 08:46:25,152 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:46:25,152 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:46:25,152 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:46:25,152 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:46:25,152 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:46:25,157 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:46:25,157 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:46:29,807 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 625 chars
2025-06-22 08:46:29,808 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that this particular asset has been experiencing an upward trajectory with diminishing returns, suggestin...
2025-06-22 08:46:29,808 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 149, Total: 271
2025-06-22 08:46:29,809 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT THIS PARTICULAR ASSET HAS BEEN EXPERIENCING AN UPWARD TRAJECTORY WITH DIMINISHING RETURNS, SUGGESTING IT MAY BE APPROACHING ITS PEAK VALUE SOON. GIVEN THESE FACTORS AND CONSIDERING TYPICAL VOLATILITY PATTERNS ASSOCIATED WITH SIMILAR ASSETS DURING THEIR DECLINE PHASE, A SHORT POSITION IS RECOMMENDED TO CAPITALIZE ON THE EXPECTED DOWNTREND WHILE LIMITING POTENTIAL LOSSES THROUGH STRATEGIC TAKE-PROFIT AND STOP-LOSS SETTINGS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:46:29,812 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:46:29,865 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:46:29,866 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:46:29,917 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:46:29,918 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:46:29,972 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:46:29,972 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.82s - 3 prompts executed sequentially
2025-06-22 08:47:32,260 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:47:32,262 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:47:32,263 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:47:32,264 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:47:32,265 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:47:32,268 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:47:32,270 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:47:37,191 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 736 chars
2025-06-22 08:47:37,192 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may start to decline, as indicated by...
2025-06-22 08:47:37,193 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 171, Total: 293
2025-06-22 08:47:37,199 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SUGGEST THAT WE ARE APPROACHING AN INFLECTION POINT WHERE PRICES MAY START TO DECLINE, AS INDICATED BY SIMILAR PAST TRENDS DURING PERIODS OF HIGH VOLATILITY AND SPECULATIVE TRADING ACTIVITY. GIVEN THIS ANALYSIS ALONG WITH OUR CONFIDENCE LEVEL BASED ON TECHNICAL INDICATORS SUCH AS MOVING AVERAGES AND RSI (RELATIVE STRENGTH INDEX), A SHORT POSITION IS RECOMMENDED FOR POTENTIAL PROFIT FROM AN ANTICIPATED PRICE CORRECTION OR REVERSAL, WHILE ALSO SETTING STOP-LOSS TO MANAGE RISK EFFECTIVELY IN CASE THE MARKET DOES NOT BEHAVE AS EXPECTED.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:47:37,214 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:47:37,280 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:47:37,282 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:47:37,342 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:47:37,343 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:47:37,402 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:47:37,403 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.14s - 3 prompts executed sequentially
2025-06-22 08:48:39,964 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:48:39,964 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:48:39,965 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:48:39,967 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:48:39,967 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:48:39,970 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:48:39,970 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:48:44,889 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 693 chars
2025-06-22 08:48:44,891 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 08:48:44,893 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 165, Total: 287
2025-06-22 08:48:44,895 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET DOES NOT CORRECT AS EXPECTED BUT ALSO SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF AN UNEXPECTED POSITIVE TURN.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:48:44,904 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:48:44,960 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:48:44,962 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:48:45,022 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:48:45,023 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:48:45,083 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:48:45,099 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.12s - 3 prompts executed sequentially
2025-06-22 08:49:46,848 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:49:46,849 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:49:46,851 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:49:46,852 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:49:46,853 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:49:46,854 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:49:46,858 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:49:52,111 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 792 chars
2025-06-22 08:49:52,112 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of Bitcoin, with its price significantly above the historical average and without strong fundamental support. Technical indicators such as RSI are in oversold terr...
2025-06-22 08:49:52,112 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 190, Total: 312
2025-06-22 08:49:52,115 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF BITCOIN, WITH ITS PRICE SIGNIFICANTLY ABOVE THE HISTORICAL AVERAGE AND WITHOUT STRONG FUNDAMENTAL SUPPORT. TECHNICAL INDICATORS SUCH AS RSI ARE IN OVERSOLD TERRITORY WHILE VOLUME IS DECLINING DESPITE RISING PRICES, SUGGESTING WEAKENING BUYER INTEREST. A SHORT POSITION COULD CAPITALIZE ON THIS DISCREPANCY BETWEEN MARKET SENTIMENT AND UNDERLYING VALUE BEFORE A POTENTIAL CORRECTION OCCURS. THE TAKE-PROFIT LEVEL OF 10% ACCOUNTS FOR THE POSSIBILITY THAT BITCOIN MAY CONTINUE TO RISE IN THE SHORT TERM DUE TO SPECULATIVE TRADING, WHILE THE STOP LOSS AT 20% PROVIDES PROTECTION AGAINST AN UNEXPECTED RALLY OR FURTHER DECLINE BEYOND OUR TARGET PRICE RANGE.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:49:52,116 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:49:52,169 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:49:52,170 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:49:52,231 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:49:52,231 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:49:52,293 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:49:52,294 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.44s - 3 prompts executed sequentially
2025-06-22 08:50:54,231 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:50:54,231 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:50:54,231 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:50:54,232 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:50:54,234 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:50:54,235 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:50:54,236 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:50:59,859 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 881 chars
2025-06-22 08:50:59,860 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that this particular asset has been on an upward trajectory for several months, but recent news about reg...
2025-06-22 08:50:59,860 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 211, Total: 333
2025-06-22 08:50:59,862 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT THIS PARTICULAR ASSET HAS BEEN ON AN UPWARD TRAJECTORY FOR SEVERAL MONTHS, BUT RECENT NEWS ABOUT REGULATORY CHANGES AND DECREASING ADOPTION RATES SUGGEST A POSSIBLE REVERSAL. GIVEN THESE FACTORS COMBINED WITH TECHNICAL INDICATORS SUCH AS RSI NEARING OVERSOLD TERRITORY AND THE MACD LINE CROSSING BELOW ITS SIGNAL LINE, IT'S ADVISABLE TO INITIATE A SHORT POSITION NOW WHILE SETTING STOP-LOSS ORDERS AT 20% LOSS THRESHOLD FOR PROTECTION AGAINST SUDDEN MARKET SWINGS. THE TAKE PROFIT IS SET AT 10%, ANTICIPATING THAT REGULATORY PRESSURES WILL CAUSE PRICES TO DROP BY THIS PERCENTAGE BEFORE STABILIZING OR REVERSING FURTHER DOWNWARDS, WHICH WOULD YIELD PROFITS FROM THE SHORT POSITION.", 'ACTION': 'ENTER_NOW'}
2025-06-22 08:50:59,864 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:50:59,930 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:50:59,931 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:50:59,992 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:50:59,994 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:51:00,054 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:51:00,055 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.82s - 3 prompts executed sequentially
2025-06-22 08:52:01,826 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:52:01,827 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:52:01,828 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:52:01,829 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:52:01,830 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:52:01,831 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:52:01,836 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:52:06,626 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 641 chars
2025-06-22 08:52:06,627 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns show that similar valuations have led to significant corrections within six months, suggesting an impe...
2025-06-22 08:52:06,628 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 158, Total: 280
2025-06-22 08:52:06,632 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SHOW THAT SIMILAR VALUATIONS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. HOWEVER, GIVEN THIS IS NOT ALWAYS CERTAIN AND THERE ARE EXTERNAL FACTORS SUCH AS REGULATORY NEWS OR TECHNOLOGICAL ADVANCEMENTS WHICH COULD AFFECT PRICES POSITIVELY, A CONSERVATIVE APPROACH WITH SET TAKE-PROFIT AT 10% ENSURES SOME PROFIT IF THE MARKET DOES CORRECT BUT ALSO SETS A STOP LOSS TO PROTECT FROM SIGNIFICANT LOSSES.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:52:06,639 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:52:06,700 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:52:06,701 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:52:06,762 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:52:06,763 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:52:06,824 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:52:06,829 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.00s - 3 prompts executed sequentially
2025-06-22 08:53:09,991 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:53:09,991 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:53:09,992 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:53:09,996 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:53:09,999 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:53:10,004 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:53:10,005 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:53:14,240 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 488 chars
2025-06-22 08:53:14,242 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within three months, suggesting an immine...
2025-06-22 08:53:14,243 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 130, Total: 252
2025-06-22 08:53:14,245 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN THREE MONTHS, SUGGESTING AN IMMINENT DECLINE. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:53:14,245 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:53:14,306 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:53:14,314 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:53:14,383 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:53:14,385 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:53:14,446 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:53:14,447 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.45s - 3 prompts executed sequentially
2025-06-22 08:54:16,258 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:54:16,258 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:54:16,258 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:54:16,260 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:54:16,260 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:54:16,260 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:54:16,262 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:54:22,013 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 995 chars
2025-06-22 08:54:22,015 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent upward trend in the cryptocurrency's price over the past month, with volume increasing steadily. However, there are signs of potential reversal due to recent regulatory news and de...
2025-06-22 08:54:22,015 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 225, Total: 347
2025-06-22 08:54:22,018 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT UPWARD TREND IN THE CRYPTOCURRENCY'S PRICE OVER THE PAST MONTH, WITH VOLUME INCREASING STEADILY. HOWEVER, THERE ARE SIGNS OF POTENTIAL REVERSAL DUE TO RECENT REGULATORY NEWS AND DECREASING SOCIAL MEDIA SENTIMENT TOWARDS THIS PARTICULAR COIN. GIVEN THESE FACTORS, I RECOMMEND TAKING A SHORT POSITION NOW AS IT SEEMS LIKELY THAT PRICES WILL BEGIN TO FALL SOON AFTER REACHING THEIR PEAK LEVELS. THE TAKE-PROFIT LEVEL IS SET AT 10% ABOVE THE CURRENT PRICE BECAUSE OF POTENTIAL RESISTANCE POINTS IDENTIFIED IN HISTORICAL DATA AND TECHNICAL ANALYSIS CHARTS. A STOP LOSS HAS BEEN PLACED AT 20% BELOW THE ENTRY POINT, WHICH PROVIDES A SAFETY NET AGAINST SIGNIFICANT LOSSES IF MY PREDICTION DOES NOT MATERIALIZE AS EXPECTED DUE TO UNFORESEEN MARKET MOVEMENTS OR EXTERNAL FACTORS THAT COULD POSITIVELY IMPACT THIS CRYPTOCURRENCY'S PRICE DESPITE CURRENT INDICATORS SUGGESTING OTHERWISE.", 'ACTION': 'ENTER_NOW'}
2025-06-22 08:54:22,025 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:54:22,085 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:54:22,087 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:54:22,148 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:54:22,150 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:54:22,210 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:54:22,212 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.95s - 3 prompts executed sequentially
2025-06-22 08:55:24,410 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:55:24,410 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:55:24,412 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:55:24,412 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:55:24,414 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:55:24,414 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:55:24,414 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:55:30,000 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 852 chars
2025-06-22 08:55:30,002 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that similar patterns have led to significa...
2025-06-22 08:55:30,002 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 206, Total: 328
2025-06-22 08:55:30,003 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SHORT TIMEFRAMES. BY TAKING A SHORT POSITION NOW, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTREND WHILE SETTING STOP-LOSS ORDERS AT 20% BELOW THE CURRENT MARKET LEVEL TO MITIGATE POTENTIAL LOSSES IF OUR PREDICTION DOES NOT MATERIALIZE AS ANTICIPATED. THE TAKE-PROFIT IS SET AT 10%, WHICH PROVIDES AN OPPORTUNITY FOR PROFIT SHOULD THERE BE A TEMPORARY REVERSAL IN TRENDS BEFORE WE REACH THIS THRESHOLD, BUT IT'S CONSERVATIVE ENOUGH GIVEN THE HIGH CONFIDENCE OF OVERVALUATION AND VOLATILITY INDICATORS POINTING TOWARDS FURTHER DECLINE.", 'ACTION': 'ENTER_NOW'}
2025-06-22 08:55:30,004 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:55:30,060 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:55:30,061 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:55:30,140 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:55:30,141 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:55:30,202 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:55:30,203 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.79s - 3 prompts executed sequentially
2025-06-22 08:56:32,006 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:56:32,007 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:56:32,008 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:56:32,011 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:56:32,012 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:56:32,016 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:56:32,017 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:56:37,225 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 839 chars
2025-06-22 08:56:37,227 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that similar patterns have led to significant corrections within six months, suggesting an impendin...
2025-06-22 08:56:37,228 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 198, Total: 320
2025-06-22 08:56:37,229 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS ANALYSIS AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET DOES NOT FOLLOW HISTORICAL TRENDS OR IS LESS AFFECTED BY NEW REGULATIONS THAN ANTICIPATED. A STOP LOSS OF 20% WILL HELP MITIGATE SIGNIFICANT LOSSES IN CASE MY ANALYSIS PROVES INCORRECT AND THE PRICE RISES UNEXPECTEDLY DUE TO UNFORESEEN POSITIVE DEVELOPMENTS WITHIN THE CRYPTOCURRENCY SECTOR.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:56:37,229 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:56:37,281 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:56:37,283 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:56:37,344 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:56:37,346 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:56:37,408 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:56:37,412 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.40s - 3 prompts executed sequentially
2025-06-22 08:57:39,531 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:57:39,532 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:57:39,533 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:57:39,536 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:57:39,537 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:57:39,538 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:57:39,538 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:57:44,337 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 693 chars
2025-06-22 08:57:44,338 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 08:57:44,340 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 165, Total: 287
2025-06-22 08:57:44,342 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET DOES NOT CORRECT AS EXPECTED BUT ALSO SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF AN UNEXPECTED POSITIVE TURN.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:57:44,347 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:57:44,400 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:57:44,402 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:57:44,462 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:57:44,465 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:57:44,524 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:57:44,526 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.99s - 3 prompts executed sequentially
2025-06-22 08:58:46,350 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:58:46,350 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:58:46,350 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:58:46,352 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:58:46,352 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:58:46,353 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:58:46,353 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:58:51,489 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 795 chars
2025-06-22 08:58:51,490 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with its price trending upwards despite lackluster fundamentals. Historical volatility is high and shows signs of an impending c...
2025-06-22 08:58:51,492 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 194, Total: 316
2025-06-22 08:58:51,494 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH ITS PRICE TRENDING UPWARDS DESPITE LACKLUSTER FUNDAMENTALS. HISTORICAL VOLATILITY IS HIGH AND SHOWS SIGNS OF AN IMPENDING CORRECTION BASED ON TECHNICAL INDICATORS SUCH AS RSI BEING ABOVE 70 (INDICATING POSSIBLE OVERSOLD CONDITIONS) WHILE MOVING AVERAGES ARE SHOWING A DOWNWARD TRAJECTORY, SUGGESTING WEAKENING MOMENTUM. THE UPCOMING EARNINGS REPORT COULD FURTHER IMPACT THE PRICE NEGATIVELY IF IT DOES NOT MEET MARKET EXPECTATIONS. THEREFORE, I RECOMMEND TAKING A SHORT POSITION WITH MODERATE RISK MANAGEMENT THROUGH SETTING TAKE-PROFIT AND STOP-LOSS LEVELS TO CAPITALIZE ON POTENTIAL DECLINES WHILE LIMITING EXPOSURE.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:58:51,495 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:58:51,553 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:58:51,554 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:58:51,614 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:58:51,616 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:58:51,692 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:58:51,694 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.34s - 3 prompts executed sequentially
2025-06-22 08:59:54,075 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 08:59:54,076 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 08:59:54,077 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 08:59:54,078 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 08:59:54,079 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 08:59:54,080 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 08:59:54,081 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 08:59:58,305 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 487 chars
2025-06-22 08:59:58,306 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 08:59:58,306 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 129, Total: 251
2025-06-22 08:59:58,308 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DECLINE WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 08:59:58,308 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 08:59:58,365 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 08:59:58,367 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 08:59:58,443 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 08:59:58,445 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 08:59:58,503 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 08:59:58,507 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.43s - 3 prompts executed sequentially
2025-06-22 09:01:00,827 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:01:00,831 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:01:00,833 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:01:00,836 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:01:00,838 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:01:00,839 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:01:00,841 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:01:05,989 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 694 chars
2025-06-22 09:01:05,990 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent downward trend in Bitcoin's price over the past week, with volume increasing as sellers step up. Technical indicators such as RSI and MACD are signaling oversold conditions which o...
2025-06-22 09:01:05,991 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 180, Total: 302
2025-06-22 09:01:05,994 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT DOWNWARD TREND IN BITCOIN'S PRICE OVER THE PAST WEEK, WITH VOLUME INCREASING AS SELLERS STEP UP. TECHNICAL INDICATORS SUCH AS RSI AND MACD ARE SIGNALING OVERSOLD CONDITIONS WHICH OFTEN PRECEDE SHORT-TERM REVERSALS BUT ALSO INDICATE POTENTIAL FOR FURTHER DECLINE IF NOT MANAGED PROPERLY. GIVEN THIS PATTERN, A SHORT POSITION COULD BE PROFITABLE IN THE NEAR TERM WITH AN APPROPRIATE TAKE PROFIT AT 10% TO CAPITALIZE ON ANY IMMEDIATE PRICE CORRECTION AND STOP LOSS SET AT 20% TO PROTECT AGAINST UNEXPECTED MARKET MOVEMENTS OR CONTINUED DOWNTREND BEYOND OUR TARGETED EXIT POINT.", 'ACTION': 'ENTER_NOW'}
2025-06-22 09:01:05,994 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:01:06,049 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:01:06,052 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:01:06,110 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:01:06,112 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:01:06,172 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:01:06,174 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.35s - 3 prompts executed sequentially
2025-06-22 09:02:07,944 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:02:07,945 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:02:07,945 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:02:07,946 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:02:07,948 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:02:07,948 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:02:07,948 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:02:12,402 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 592 chars
2025-06-22 09:02:12,404 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that similar patterns have led to significant corrections within six months, suggesting an impendin...
2025-06-22 09:02:12,404 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 151, Total: 273
2025-06-22 09:02:12,407 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. HOWEVER, CONSIDERING RECENT REGULATORY NEWS AND INCREASED INSTITUTIONAL INTEREST WHICH COULD STABILIZE PRICES TEMPORARILY, I RECOMMEND TAKING PROFITS AT A 10% GAIN WHILE SETTING THE STOP-LOSS ORDER AT 20% BELOW YOUR ENTRY PRICE AS A PRECAUTIONARY MEASURE AGAINST FURTHER DECLINES.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:02:12,408 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:02:12,479 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:02:12,480 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:02:12,541 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:02:12,543 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:02:12,603 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:02:12,607 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.66s - 3 prompts executed sequentially
2025-06-22 09:03:14,375 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:03:14,376 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:03:14,377 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:03:14,379 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:03:14,379 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:03:14,380 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:03:14,381 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:03:18,769 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 486 chars
2025-06-22 09:03:18,773 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an imminent...
2025-06-22 09:03:18,774 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 130, Total: 252
2025-06-22 09:03:18,785 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMMINENT DECLINE. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:03:18,788 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:03:18,851 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:03:18,852 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:03:18,929 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:03:18,931 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:03:18,991 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:03:18,992 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.62s - 3 prompts executed sequentially
2025-06-22 09:04:20,787 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:04:20,787 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:04:20,788 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:04:20,790 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:04:20,790 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:04:20,791 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:04:20,791 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:04:25,534 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 619 chars
2025-06-22 09:04:25,535 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns suggest that we are approaching an inflection point where prices may begin to decline sharply, as indi...
2025-06-22 09:04:25,536 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 156, Total: 278
2025-06-22 09:04:25,540 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SUGGEST THAT WE ARE APPROACHING AN INFLECTION POINT WHERE PRICES MAY BEGIN TO DECLINE SHARPLY, AS INDICATED BY RECENT BEARISH CANDLESTICK FORMATIONS AND INCREASING VOLUME ON SELLING DAYS. GIVEN THIS ANALYSIS, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT AND 20% STOP LOSS TO CAPITALIZE ON THE ANTICIPATED DOWNWARD PRICE MOVEMENT WHILE MANAGING POTENTIAL LOSSES EFFECTIVELY.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:04:25,542 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:04:25,606 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:04:25,607 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:04:25,667 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:04:25,675 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:04:25,730 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:04:25,730 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.94s - 3 prompts executed sequentially
2025-06-22 09:05:27,803 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:05:27,804 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:05:27,805 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:05:27,807 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:05:27,808 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:05:27,819 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:05:27,820 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:05:33,318 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 901 chars
2025-06-22 09:05:33,319 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 09:05:33,319 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 211, Total: 333
2025-06-22 09:05:33,320 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET DOES NOT FOLLOW HISTORICAL TRENDS OR IS LESS AFFECTED BY NEW REGULATIONS THAN ANTICIPATED. HOWEVER, DUE TO HIGH VOLATILITY IN CRYPTOCURRENCY MARKETS AND UNPREDICTABLE EXTERNAL FACTORS SUCH AS TECHNOLOGICAL ADVANCEMENTS OR SHIFTS IN INVESTOR BEHAVIOR, A STOP LOSS AT 20% WILL PROTECT AGAINST SIGNIFICANT LOSSES IF THE MARKET TURNS UNFAVORABLY QUICKLY.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:05:33,320 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:05:33,372 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:05:33,375 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:05:33,435 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:05:33,435 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:05:33,496 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:05:33,504 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.69s - 3 prompts executed sequentially
2025-06-22 09:06:35,644 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:06:35,644 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:06:35,645 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:06:35,646 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:06:35,647 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:06:35,647 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:06:35,647 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:06:40,212 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 619 chars
2025-06-22 09:06:40,216 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that similar patterns have led to significant corrections within six months, suggesting an impendin...
2025-06-22 09:06:40,217 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 155, Total: 277
2025-06-22 09:06:40,220 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS ANALYSIS AND CONSIDERING TYPICAL VOLATILITY LEVELS FOR SUCH ASSETS, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE-PROFIT AND 20% STOP LOSS TO CAPITALIZE ON THE EXPECTED PRICE DECLINE WHILE LIMITING POTENTIAL LOSSES IF MARKET CONDITIONS CHANGE UNEXPECTEDLY.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:06:40,222 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:06:40,277 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:06:40,280 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:06:40,339 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:06:40,344 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:06:40,401 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:06:40,402 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.76s - 3 prompts executed sequentially
2025-06-22 09:07:42,264 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:07:42,265 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:07:42,265 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:07:42,267 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:07:42,267 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:07:42,268 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:07:42,269 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:07:47,792 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 802 chars
2025-06-22 09:07:47,793 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that it has been consistently rising without significant support levels or volume indicators to sug...
2025-06-22 09:07:47,794 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 200, Total: 322
2025-06-22 09:07:47,797 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT IT HAS BEEN CONSISTENTLY RISING WITHOUT SIGNIFICANT SUPPORT LEVELS OR VOLUME INDICATORS TO SUGGEST SUSTAINABILITY. TECHNICAL ANALYSIS REVEALS AN UPWARD TRAJECTORY WITH DIMINISHING TRADING VOLUMES, WHICH OFTEN PRECEDES CORRECTIONS. THE RSI (RELATIVE STRENGTH INDEX) IS ABOVE 70, INDICATING OVERBOUGHT CONDITIONS. GIVEN THESE FACTORS AND THE RECENT NEWS OF REGULATORY CRACKDOWNS IN SEVERAL MAJOR MARKETS THAT COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, A SHORT POSITION SEEMS PRUDENT WITH SET TAKE-PROFIT AT 10% TO CAPITALIZE ON POTENTIAL PRICE DROPS WHILE LIMITING LOSSES THROUGH STOP LOSS AT 20%.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:07:47,799 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:07:47,857 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:07:47,857 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:07:47,918 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:07:47,922 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:07:47,997 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:07:48,000 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.73s - 3 prompts executed sequentially
2025-06-22 09:08:50,839 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:08:50,839 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:08:50,840 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:08:50,849 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:08:50,850 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:08:50,867 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:08:50,874 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:08:55,124 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 449 chars
2025-06-22 09:08:55,126 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that this could be an inflated bubble ready...
2025-06-22 09:08:55,127 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 123, Total: 245
2025-06-22 09:08:55,130 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT THIS COULD BE AN INFLATED BUBBLE READY TO BURST. A SHORT POSITION WOULD CAPITALIZE ON EXPECTED DOWNWARD MOVEMENT WHILE LIMITING RISK EXPOSURE THROUGH SET TAKE-PROFIT AND STOP-LOSS LEVELS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:08:55,131 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:08:55,188 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:08:55,188 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:08:55,251 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:08:55,254 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:08:55,331 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:08:55,336 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.49s - 3 prompts executed sequentially
2025-06-22 09:09:57,104 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:09:57,105 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:09:57,107 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:09:57,110 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:09:57,117 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:09:57,118 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:09:57,120 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:10:01,892 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 664 chars
2025-06-22 09:10:01,893 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns show that similar trends have led to significant corrections within six months, suggesting an impendin...
2025-06-22 09:10:01,893 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 162, Total: 284
2025-06-22 09:10:01,896 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SHOW THAT SIMILAR TRENDS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS ANALYSIS AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET REVERSES EARLY BUT ALSO SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF FURTHER DECLINE.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:10:01,896 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:10:01,953 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:10:01,957 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:10:02,017 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:10:02,026 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:10:02,092 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:10:02,094 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.99s - 3 prompts executed sequentially
2025-06-22 09:11:04,591 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:11:04,592 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:11:04,597 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:11:04,600 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:11:04,604 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:11:04,605 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:11:04,610 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:11:09,402 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 629 chars
2025-06-22 09:11:09,405 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price patterns show that similar trends have led to significant corrections within six months, suggesting an impendin...
2025-06-22 09:11:09,406 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 156, Total: 278
2025-06-22 09:11:09,414 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE PATTERNS SHOW THAT SIMILAR TRENDS HAVE LED TO SIGNIFICANT CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH MAY NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET REVERSES EARLY WHILE SETTING STOP LOSS ORDERS TO LIMIT DOWNSIDE RISKS UP TO 20%.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:11:09,426 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:11:09,491 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:11:09,502 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:11:09,569 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:11:09,579 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:11:09,660 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:11:09,673 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.07s - 3 prompts executed sequentially
2025-06-22 09:12:11,785 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:12:11,787 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:12:11,789 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:12:11,792 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:12:11,795 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:12:11,798 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:12:11,800 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:12:17,300 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 874 chars
2025-06-22 09:12:17,302 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that this particular asset has been experiencing an upward trajectory for several months, which is not su...
2025-06-22 09:12:17,305 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 209, Total: 331
2025-06-22 09:12:17,310 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT THIS PARTICULAR ASSET HAS BEEN EXPERIENCING AN UPWARD TRAJECTORY FOR SEVERAL MONTHS, WHICH IS NOT SUSTAINABLE GIVEN ITS VOLATILE NATURE AND LACK OF FUNDAMENTAL BACKING SUCH AS UTILITY OR WIDESPREAD ADOPTION. THE SHORT INTEREST RATIO SUGGESTS A HIGH LEVEL OF BEARISH SENTIMENT AMONG TRADERS, INDICATING POTENTIAL DOWNSIDE PRESSURE IN THE NEAR TERM. HOWEVER, CONSIDERING MARKET LIQUIDITY AND RECENT REGULATORY NEWS THAT COULD IMPACT INVESTOR CONFIDENCE NEGATIVELY, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS TO CAPITALIZE ON EXPECTED PRICE DECLINES WHILE LIMITING EXPOSURE THROUGH TAKE-PROFIT AT 10% ABOVE ENTRY POINT AND STOP LOSS SET AT 20% BELOW.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:12:17,317 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:12:17,382 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:12:17,384 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:12:17,477 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:12:17,478 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:12:17,553 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:12:17,556 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.77s - 3 prompts executed sequentially
2025-06-22 09:13:19,595 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:13:19,596 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:13:19,604 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:13:19,608 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:13:19,614 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:13:19,619 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:13:19,627 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:13:25,158 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 901 chars
2025-06-22 09:13:25,160 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent upward trend in the price of Bitcoin over the past three months, with an average monthly increase of approximately 8%. However, there are indicators suggesting that this growth may...
2025-06-22 09:13:25,161 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 208, Total: 330
2025-06-22 09:13:25,163 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT UPWARD TREND IN THE PRICE OF BITCOIN OVER THE PAST THREE MONTHS, WITH AN AVERAGE MONTHLY INCREASE OF APPROXIMATELY 8%. HOWEVER, THERE ARE INDICATORS SUGGESTING THAT THIS GROWTH MAY BE UNSUSTAINABLE. TECHNICAL ANALYSIS REVEALS SEVERAL BEARISH PATTERNS ON THE CHART, INCLUDING HIGHER LOWS AND LOWER HIGHS WITHIN A NARROWING RANGE. ADDITIONALLY, SENTIMENT ANALYSIS FROM SOCIAL MEDIA PLATFORMS SHOWS INCREASING NEGATIVITY AMONG INVESTORS REGARDING BITCOIN'S FUTURE PERFORMANCE. GIVEN THESE FACTORS, I RECOMMEND TAKING A SHORT POSITION WITH MODERATE CONFIDENCE IN ANTICIPATION OF POTENTIAL PRICE CORRECTIONS OR REVERSALS. THE TAKE-PROFIT AND STOP-LOSS LEVELS ARE SET TO PROTECT AGAINST SIGNIFICANT LOSSES WHILE STILL ALLOWING FOR SOME PROFIT IF THE MARKET DOES NOT FOLLOW MY PREDICTION.", 'ACTION': 'ENTER_NOW'}
2025-06-22 09:13:25,165 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:13:25,231 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:13:25,235 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:13:25,292 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:13:25,295 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:13:25,354 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:13:25,357 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.76s - 3 prompts executed sequentially
2025-06-22 09:14:27,537 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:14:27,544 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:14:27,545 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:14:27,547 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:14:27,553 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:14:27,556 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:14:27,563 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:14:32,472 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 663 chars
2025-06-22 09:14:32,474 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Short positions are recommended as they could benefit from an anticip...
2025-06-22 09:14:32,476 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 169, Total: 291
2025-06-22 09:14:32,484 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. SHORT POSITIONS ARE RECOMMENDED AS THEY COULD BENEFIT FROM AN ANTICIPATED CORRECTION OR DECLINE BASED ON HISTORICAL TRENDS DURING SIMILAR CONDITIONS. A TAKE-PROFIT LEVEL IS SET AT 10% TO CAPITALIZE ON A POTENTIAL SHORT-TERM GAIN IF THE MARKET DOES NOT FOLLOW ITS USUAL PATTERN, WHILE A STOP LOSS OF 20% ENSURES PROTECTION AGAINST SIGNIFICANT LOSSES IN CASE OF AN UNEXPECTED UPWARD MOVEMENT DUE TO UNFORESEEN POSITIVE NEWS OR EVENTS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:14:32,485 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:14:32,557 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:14:32,578 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:14:32,650 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:14:32,653 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:14:32,712 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:14:32,716 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.17s - 3 prompts executed sequentially
2025-06-22 09:15:34,794 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:15:34,795 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:15:34,795 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:15:34,798 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:15:34,799 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:15:34,799 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:15:34,803 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:15:39,158 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 479 chars
2025-06-22 09:15:39,159 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within two weeks, suggesting an imminent ...
2025-06-22 09:15:39,163 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 136, Total: 258
2025-06-22 09:15:39,164 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN TWO WEEKS, SUGGESTING AN IMMINENT DECLINE. BY SHORTING NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE LIMITING OUR RISK EXPOSURE THROUGH A TAKE-PROFIT AT 10% AND STOP LOSS AT 20%.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:15:39,166 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:15:39,234 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:15:39,239 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:15:39,311 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:15:39,314 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:15:39,375 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:15:39,380 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.58s - 3 prompts executed sequentially
2025-06-22 09:16:41,216 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:16:41,217 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:16:41,218 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:16:41,226 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:16:41,233 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:16:41,238 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:16:41,241 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:16:46,130 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 689 chars
2025-06-22 09:16:46,130 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent upward trend in Bitcoin's price over the past three months, with an average monthly increase of approximately 8%. However, there are signs that this growth may be slowing down. Vol...
2025-06-22 09:16:46,130 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 175, Total: 297
2025-06-22 09:16:46,132 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT UPWARD TREND IN BITCOIN'S PRICE OVER THE PAST THREE MONTHS, WITH AN AVERAGE MONTHLY INCREASE OF APPROXIMATELY 8%. HOWEVER, THERE ARE SIGNS THAT THIS GROWTH MAY BE SLOWING DOWN. VOLUME HAS DECREASED BY 15% COMPARED TO LAST MONTH WHILE TECHNICAL INDICATORS SUCH AS RSI AND MACD HAVE SHOWN DIVERGENCE FROM THEIR LONG-TERM TRENDS SUGGESTING A POTENTIAL REVERSAL IN THE NEAR FUTURE. THE SHORT POSITION IS RECOMMENDED WITH MODERATE CONFIDENCE DUE TO THESE FACTORS, BUT IT'S IMPORTANT TO MONITOR MARKET SENTIMENT CLOSELY FOR ANY SUDDEN CHANGES THAT COULD AFFECT THIS DECISION.", 'ACTION': 'ENTER_NOW'}
2025-06-22 09:16:46,133 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:16:46,197 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:16:46,200 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:16:46,260 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:16:46,261 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:16:46,322 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:16:46,324 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.11s - 3 prompts executed sequentially
2025-06-22 09:17:48,148 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:17:48,150 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:17:48,156 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:17:48,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:17:48,163 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:17:48,165 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:17:48,170 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:17:52,658 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 512 chars
2025-06-22 09:17:52,659 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question, with high trading volumes and increasing price volatility. Historical trends suggest that this could be an inflated bubble ready...
2025-06-22 09:17:52,661 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 141, Total: 263
2025-06-22 09:17:52,667 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION, WITH HIGH TRADING VOLUMES AND INCREASING PRICE VOLATILITY. HISTORICAL TRENDS SUGGEST THAT THIS COULD BE AN INFLATED BUBBLE READY TO BURST. A SHORT POSITION WOULD CAPITALIZE ON EXPECTED DECLINES WHILE LIMITING RISK EXPOSURE THROUGH SET TAKE-PROFIT LEVELS AT A 10% GAIN THRESHOLD AND STOP LOSS LIMITS THE POTENTIAL DOWNSIDE WITH A CAP OF 20%.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:17:52,669 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:17:52,733 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:17:52,746 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:17:52,812 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:17:52,818 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:17:52,888 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:17:52,889 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.74s - 3 prompts executed sequentially
2025-06-22 09:18:54,871 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:18:54,872 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:18:54,873 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:18:54,874 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:18:54,876 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:18:54,879 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:18:54,882 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:18:59,220 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 486 chars
2025-06-22 09:18:59,222 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an imminent...
2025-06-22 09:18:59,223 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 130, Total: 252
2025-06-22 09:18:59,227 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMMINENT DECLINE. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:18:59,234 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:18:59,290 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:18:59,293 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:18:59,352 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:18:59,353 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:18:59,416 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:18:59,418 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.54s - 3 prompts executed sequentially
2025-06-22 09:20:02,142 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:20:02,148 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:20:02,153 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:20:02,156 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:20:02,157 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:20:02,158 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:20:02,160 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:20:07,332 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 831 chars
2025-06-22 09:20:07,333 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical price trends show that it has been consistently rising without significant support levels or volume indicators to sug...
2025-06-22 09:20:07,334 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 193, Total: 315
2025-06-22 09:20:07,337 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL PRICE TRENDS SHOW THAT IT HAS BEEN CONSISTENTLY RISING WITHOUT SIGNIFICANT SUPPORT LEVELS OR VOLUME INDICATORS TO SUGGEST SUSTAINABILITY, WHICH OFTEN PRECEDES CORRECTIONS. GIVEN THIS PATTERN AND CONSIDERING RECENT NEWS ABOUT REGULATORY CHANGES POTENTIALLY IMPACTING ITS VALUE NEGATIVELY, A SHORT POSITION COULD BE PROFITABLE IF THE MARKET CORRECTS AS EXPECTED. THE TAKE-PROFIT IS SET AT 10% ABOVE CURRENT LEVELS DUE TO POTENTIAL VOLATILITY IN RESPONSE TO EXTERNAL FACTORS; HOWEVER, WITH HIGH CONFIDENCE (85%) BASED ON TECHNICAL AND FUNDAMENTAL ANALYSIS, THIS STRATEGY SEEMS VIABLE FOR EXPERIENCED TRADERS WHO UNDERSTAND SHORT POSITIONS' RISKS WELL.", 'ACTION': 'ENTER_NOW'}
2025-06-22 09:20:07,338 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:20:07,391 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:20:07,392 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:20:07,453 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:20:07,456 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:20:07,517 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:20:07,520 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.37s - 3 prompts executed sequentially
2025-06-22 09:21:09,552 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:21:09,552 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:21:09,552 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:21:09,563 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:21:09,565 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:21:09,573 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:21:09,575 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:21:13,744 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 485 chars
2025-06-22 09:21:13,745 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within two weeks, suggesting an imminent ...
2025-06-22 09:21:13,746 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 130, Total: 252
2025-06-22 09:21:13,748 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN TWO WEEKS, SUGGESTING AN IMMINENT DECLINE. BY TAKING A SHORT POSITION NOW WITH THESE PARAMETERS SET, WE CAN CAPITALIZE ON THIS EXPECTED DOWNTURN WHILE LIMITING OUR RISK EXPOSURE THROUGH STOP-LOSS AND TAKE-PROFIT ORDERS.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:21:13,751 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:21:13,806 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:21:13,808 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:21:13,869 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:21:13,882 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:21:13,945 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:21:13,948 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 4.39s - 3 prompts executed sequentially
2025-06-22 09:22:15,844 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:22:15,846 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:22:15,847 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:22:15,851 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:22:15,854 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:22:15,857 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:22:15,858 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:22:21,006 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 667 chars
2025-06-22 09:22:21,007 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a potential overvaluation of the cryptocurrency in question. Historical trends show that similar patterns have led to significant price corrections within six months, suggesting an impendin...
2025-06-22 09:22:21,007 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 162, Total: 284
2025-06-22 09:22:21,010 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A POTENTIAL OVERVALUATION OF THE CRYPTOCURRENCY IN QUESTION. HISTORICAL TRENDS SHOW THAT SIMILAR PATTERNS HAVE LED TO SIGNIFICANT PRICE CORRECTIONS WITHIN SIX MONTHS, SUGGESTING AN IMPENDING DOWNTURN. GIVEN THIS INFORMATION AND CONSIDERING RECENT REGULATORY NEWS WHICH COULD NEGATIVELY IMPACT INVESTOR SENTIMENT, I RECOMMEND INITIATING A SHORT POSITION WITH MODERATE RISK PARAMETERS SET AT 10% TAKE PROFIT FOR POTENTIAL GAINS IF THE MARKET REVERSES EARLY BUT ALSO SETTING A STOP LOSS AT 20% TO PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF FURTHER DECLINE.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:22:21,010 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:22:21,067 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:22:21,078 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:22:21,145 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:22:21,154 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:22:21,225 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:22:21,227 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.38s - 3 prompts executed sequentially
2025-06-22 09:23:23,006 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:23:23,006 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:23:23,007 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:23:23,008 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:23:23,008 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:23:23,009 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:23:23,011 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:23:28,555 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 770 chars
2025-06-22 09:23:28,556 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent upward trend in Bitcoin's price over the past week, with an average daily increase of 3%. However, there are indicators suggesting that this growth may be unsustainable. Technical ...
2025-06-22 09:23:28,557 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 195, Total: 317
2025-06-22 09:23:28,559 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT UPWARD TREND IN BITCOIN'S PRICE OVER THE PAST WEEK, WITH AN AVERAGE DAILY INCREASE OF 3%. HOWEVER, THERE ARE INDICATORS SUGGESTING THAT THIS GROWTH MAY BE UNSUSTAINABLE. TECHNICAL ANALYSIS REVEALS SEVERAL BEARISH PATTERNS ON THE CHART, INCLUDING HIGHER HIGHS AND LOWER LOWS WITHOUT A CLEAR REVERSAL SIGNAL YET PRESENT. ADDITIONALLY, MARKET SENTIMENT IS SHOWING SIGNS OF FATIGUE WITH INCREASING MENTIONS OF OVERVALUATION IN SOCIAL MEDIA DISCUSSIONS AMONG TRADERS. GIVEN THESE FACTORS, I RECOMMEND INITIATING A SHORT POSITION TO CAPITALIZE ON POTENTIAL PRICE CORRECTIONS WHILE SETTING TAKE-PROFIT AT 10% AND STOP LOSS AT 20% TO MANAGE RISK EFFECTIVELY.", 'ACTION': 'ENTER_NOW'}
2025-06-22 09:23:28,560 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:23:28,618 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:23:28,619 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:23:28,681 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:23:28,688 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:23:28,757 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:23:28,763 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.75s - 3 prompts executed sequentially
2025-06-22 09:24:31,342 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:24:31,343 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:24:31,343 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:24:31,345 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:24:31,358 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:24:31,360 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:24:31,361 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:24:36,281 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 720 chars
2025-06-22 09:24:36,281 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data shows a consistent downward trend in Bitcoin's price over the past week, with volume increasing as sellers step up. Technical indicators such as RSI and Moving Averages are signaling an oversold cond...
2025-06-22 09:24:36,282 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 180, Total: 302
2025-06-22 09:24:36,284 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': "THE CURRENT MARKET DATA SHOWS A CONSISTENT DOWNWARD TREND IN BITCOIN'S PRICE OVER THE PAST WEEK, WITH VOLUME INCREASING AS SELLERS STEP UP. TECHNICAL INDICATORS SUCH AS RSI AND MOVING AVERAGES ARE SIGNALING AN OVERSOLD CONDITION WHICH OFTEN PRECEDES A REVERSAL BUT GIVEN RECENT REGULATORY NEWS THAT COULD IMPACT MARKET SENTIMENT NEGATIVELY, IT IS ADVISABLE TO TAKE A SHORT POSITION NOW FOR POTENTIAL PROFIT IF THE TREND CONTINUES DOWNWARDS BEFORE ANY POSSIBLE REBOUND. THE STOP-LOSS WILL PROTECT AGAINST SIGNIFICANT LOSSES IN CASE OF UNEXPECTED POSITIVE DEVELOPMENTS OR ERRATIC PRICE MOVEMENTS DUE TO EXTERNAL FACTORS LIKE GEOPOLITICAL EVENTS.", 'ACTION': 'ENTER_NOW'}
2025-06-22 09:24:36,284 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:24:36,336 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:24:36,339 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:24:36,396 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:24:36,398 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:24:36,458 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:24:36,461 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.12s - 3 prompts executed sequentially
2025-06-22 09:25:38,222 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-06-22 09:25:38,226 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-06-22 09:25:38,233 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-06-22 09:25:38,235 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-22 09:25:38,242 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-22 09:25:38,244 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-06-22 09:25:38,252 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-06-22 09:25:43,638 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 800 chars
2025-06-22 09:25:43,641 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT
CONFIDENCE: 85%
TAKE_PROFIT: 10%
STOP_LOSS: 20%
EXPLANation: The current market data indicates a bullish trend with increasing volume and higher highs, suggesting that the cryptocurrency is in an uptrend. However, there are signs of potential reversal due to increased volatility and ...
2025-06-22 09:25:43,641 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 122, Completion: 202, Total: 324
2025-06-22 09:25:43,643 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 85.0, 'EXPLANATION': 'THE CURRENT MARKET DATA INDICATES A BULLISH TREND WITH INCREASING VOLUME AND HIGHER HIGHS, SUGGESTING THAT THE CRYPTOCURRENCY IS IN AN UPTREND. HOWEVER, THERE ARE SIGNS OF POTENTIAL REVERSAL DUE TO INCREASED VOLATILITY AND DIVERGENCE BETWEEN PRICE ACTION AND MOMENTUM INDICATORS SUCH AS RSI (RELATIVE STRENGTH INDEX) WHICH HAS BEEN OVERBOUGHT FOR A PROLONGED PERIOD BUT SHOWS SOME WEAKENING UPWARD TREND LINES ON THE CHART. THE SHORT POSITION COULD CAPITALIZE ON THIS TEMPORARY OVERSOLD CONDITION BEFORE IT POTENTIALLY REVERSES BACK TO ITS UPTREND, WITH STOP LOSS SET AT 20% TO PROTECT AGAINST SUDDEN MARKET MOVEMENTS AND TAKE PROFIT LEVEL OF 10% CONSIDERING A POSSIBLE REBOUND IN PRICE AFTER HITTING OUR TARGET SELL POINT.', 'ACTION': 'ENTER_NOW'}
2025-06-22 09:25:43,644 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-06-22 09:25:43,703 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-06-22 09:25:43,708 - core.llm_orchestrator - ERROR - Error in entry timing: 'TradingContext' object is not subscriptable
2025-06-22 09:25:43,767 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-06-22 09:25:43,775 - core.llm_orchestrator - ERROR - Error in opportunity scanner: 'TradingContext' object is not subscriptable
2025-06-22 09:25:43,861 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-06-22 09:25:43,863 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 5.64s - 3 prompts executed sequentially
