"""
Matrix Theme for Epinnox v6 Trading System
Centralized theme management for consistent styling
"""

class MatrixTheme:
    """Matrix theme color and style definitions"""
    
    # Colors
    BLACK = "#000000"
    GREEN = "#00FF00"
    DARK_GREEN = "#003300"
    MID_GREEN = "#005500"
    LIGHT_GREEN = "#00AA00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    BLUE = "#0088FF"
    GRAY = "#666666"
    BRIGHT_GREEN = "#00FF88"

    # Additional aliases for compatibility
    DARK_BG = BLACK
    FONT_SIZE_MEDIUM = 12

    # Fonts - AGGRESSIVE sizing for readability
    FONT_FAMILY = "Consolas, 'Courier New', monospace"
    FONT_SIZE = 14          # Increased from 11
    FONT_SIZE_LARGE = 18    # Increased from 14
    FONT_SIZE_SMALL = 12    # Increased from 10
    FONT_SIZE_XLARGE = 22   # Increased from 16
    FONT_SIZE_HUGE = 26     # New size for headers
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        /* Main Application Style - AGGRESSIVE SIZING */
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: {cls.FONT_FAMILY};
            font-size: {cls.FONT_SIZE}px;
        }}

        /* LARGE text for readability */
        QLabel {{
            font-size: {cls.FONT_SIZE}px;
            color: {cls.GREEN};
            padding: 2px;
            margin: 1px;
        }}

        QLabel[class="title"] {{
            font-size: {cls.FONT_SIZE_LARGE}px;
            font-weight: bold;
            padding: 4px;
        }}

        QLabel[class="header"] {{
            font-size: {cls.FONT_SIZE_HUGE}px;
            font-weight: bold;
            color: {cls.BRIGHT_GREEN};
            padding: 6px;
        }}

        /* Buttons with larger text */
        QPushButton {{
            font-size: {cls.FONT_SIZE}px;
            padding: 8px 12px;
            margin: 2px;
            min-height: 30px;
        }}

        /* Tables with larger text */
        QTableWidget {{
            font-size: {cls.FONT_SIZE}px;
            gridline-color: {cls.DARK_GREEN};
        }}

        QTableWidget::item {{
            padding: 6px;
            min-height: 25px;
        }}

        /* Text areas with larger fonts */
        QTextEdit, QPlainTextEdit {{
            font-size: {cls.FONT_SIZE}px;
            line-height: 1.4;
            padding: 4px;
        }}

        /* Spinboxes and inputs */
        QSpinBox, QDoubleSpinBox, QLineEdit {{
            font-size: {cls.FONT_SIZE}px;
            padding: 6px;
            min-height: 25px;
        }}
        
        /* Tab Widget */
        QTabWidget::pane {{
            border: 1px solid {cls.MID_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.MID_GREEN};
            font-family: {cls.FONT_FAMILY};
            font-weight: bold;
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.MID_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
        }}
        
        QTabBar::tab:hover {{
            background-color: {cls.MID_GREEN};
        }}
        
        /* Labels */
        QLabel {{
            color: {cls.GREEN};
            font-family: {cls.FONT_FAMILY};
            background-color: transparent;
        }}
        
        /* Buttons */
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 1px solid {cls.MID_GREEN};
            padding: 6px 12px;
            font-family: {cls.FONT_FAMILY};
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {cls.MID_GREEN};
            border: 1px solid {cls.GREEN};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:disabled {{
            background-color: {cls.BLACK};
            color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        /* Input Fields */
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.MID_GREEN};
            padding: 4px;
            font-family: {cls.FONT_FAMILY};
        }}
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border: 2px solid {cls.GREEN};
        }}
        
        /* Text Areas */
        QTextEdit, QPlainTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.MID_GREEN};
            font-family: {cls.FONT_FAMILY};
            font-size: {cls.FONT_SIZE_SMALL}px;
        }}
        
        /* Tables */
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.MID_GREEN};
            font-family: {cls.FONT_FAMILY};
        }}
        
        QTableWidget::item {{
            padding: 4px;
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.MID_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 6px;
            border: 1px solid {cls.MID_GREEN};
            font-weight: bold;
        }}
        
        /* Group Boxes */
        QGroupBox {{
            color: {cls.GREEN};
            border: 2px solid {cls.MID_GREEN};
            border-radius: 5px;
            margin: 10px 0px;
            padding-top: 10px;
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        
        /* Progress Bars */
        QProgressBar {{
            border: 1px solid {cls.MID_GREEN};
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            text-align: center;
            font-family: {cls.FONT_FAMILY};
        }}
        
        QProgressBar::chunk {{
            background-color: {cls.GREEN};
        }}
        
        /* Scroll Bars */
        QScrollBar:vertical {{
            background-color: {cls.BLACK};
            width: 15px;
            border: 1px solid {cls.MID_GREEN};
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {cls.MID_GREEN};
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {cls.GREEN};
        }}
        
        /* Status Bar */
        QStatusBar {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border-top: 1px solid {cls.MID_GREEN};
        }}
        
        /* Splitter */
        QSplitter::handle {{
            background-color: {cls.MID_GREEN};
        }}
        
        /* Check Boxes */
        QCheckBox {{
            color: {cls.GREEN};
            font-family: {cls.FONT_FAMILY};
        }}
        
        QCheckBox::indicator {{
            width: 13px;
            height: 13px;
            border: 1px solid {cls.MID_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {cls.GREEN};
        }}
        """
    
    @classmethod
    def get_table_stylesheet(cls):
        """Get Matrix theme stylesheet specifically for tables"""
        return f"""
            QTableWidget {{
                background-color: {cls.BLACK};
                gridline-color: {cls.DARK_GREEN};
            }}
            QTableWidget::item {{
                background-color: #001100;
                color: {cls.GREEN};
            }}
            QHeaderView::section {{
                background-color: {cls.DARK_GREEN};
                color: {cls.GREEN};
            }}
            QTableWidget QTableCornerButton::section {{
                background-color: {cls.BLACK};
            }}
            QTableWidget::viewport {{
                background-color: {cls.BLACK};
            }}
        """
