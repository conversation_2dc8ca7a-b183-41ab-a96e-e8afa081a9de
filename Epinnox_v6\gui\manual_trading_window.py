#!/usr/bin/env python3
"""
Manual Trading Window for Epinnox v6
Dedicated window for manual trading controls, separated from main dashboard
"""

import sys
import logging
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QComboBox, QDoubleSpinBox, QSpinBox,
    QGroupBox, QFrame, QMessageBox, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette

logger = logging.getLogger(__name__)

class ManualTradingWindow(QMainWindow):
    """
    Dedicated manual trading window with clean, professional layout
    """
    
    # Signals for communication with main dashboard
    trade_executed = pyqtSignal(str, str, float, float, bool, float)  # symbol, side, quantity, price, success, pnl
    position_closed = pyqtSignal(str, bool)  # symbol, success
    orders_cancelled = pyqtSignal(bool)  # success
    
    def __init__(self, main_interface=None):
        """Initialize manual trading window"""
        super().__init__()
        
        # Reference to main trading interface for shared functionality
        self.main_interface = main_interface
        
        # Window properties
        self.setWindowTitle("Epinnox v6 - Manual Trading")
        self.setFixedSize(400, 600)  # Compact, fixed size
        
        # Initialize UI
        self.setup_ui()
        self.setup_styles()
        
        # Update timer for real-time data
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_displays)
        self.update_timer.start(1000)  # Update every second
        
        logger.info("📊 Manual Trading Window initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # Title
        title_label = QLabel("Manual Trading Controls")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2E86AB;
                padding: 10px;
                border-bottom: 2px solid #2E86AB;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # Trading Parameters Section
        self.create_trading_parameters_section(main_layout)
        
        # Market Data Section
        self.create_market_data_section(main_layout)
        
        # Trading Buttons Section
        self.create_trading_buttons_section(main_layout)
        
        # Position Management Section
        self.create_position_management_section(main_layout)
        
        # Account Information Section
        self.create_account_info_section(main_layout)
        
        # Add stretch to push everything to top
        main_layout.addStretch()
    
    def create_trading_parameters_section(self, parent_layout):
        """Create trading parameters section"""
        group = QGroupBox("Trading Parameters")
        layout = QGridLayout(group)
        layout.setSpacing(8)
        
        # Symbol selection
        layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            "DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", 
            "ADA/USDT:USDT", "SOL/USDT:USDT", "MATIC/USDT:USDT"
        ])
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        layout.addWidget(self.symbol_combo, 0, 1)
        
        # Quantity
        layout.addWidget(QLabel("Quantity:"), 1, 0)
        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setRange(0.0001, 100000)
        self.quantity_spinbox.setDecimals(4)
        self.quantity_spinbox.setValue(50.0)
        self.quantity_spinbox.setSuffix(" USDT")
        layout.addWidget(self.quantity_spinbox, 1, 1)
        
        # Leverage
        layout.addWidget(QLabel("Leverage:"), 2, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 125)
        self.leverage_spinbox.setValue(20)
        self.leverage_spinbox.setSuffix("x")
        self.leverage_spinbox.valueChanged.connect(self.on_leverage_changed)
        layout.addWidget(self.leverage_spinbox, 2, 1)
        
        # Price (for limit orders)
        layout.addWidget(QLabel("Limit Price:"), 3, 0)
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.000001, 1000000)
        self.price_spinbox.setDecimals(6)
        self.price_spinbox.setValue(0.0)
        layout.addWidget(self.price_spinbox, 3, 1)
        
        parent_layout.addWidget(group)
    
    def create_market_data_section(self, parent_layout):
        """Create market data display section"""
        group = QGroupBox("Market Data")
        layout = QGridLayout(group)
        layout.setSpacing(5)
        
        # Best Bid
        layout.addWidget(QLabel("Best Bid:"), 0, 0)
        self.best_bid_label = QLabel("--")
        self.best_bid_label.setStyleSheet("color: #00AA00; font-weight: bold;")
        layout.addWidget(self.best_bid_label, 0, 1)
        
        # Best Ask
        layout.addWidget(QLabel("Best Ask:"), 1, 0)
        self.best_ask_label = QLabel("--")
        self.best_ask_label.setStyleSheet("color: #AA0000; font-weight: bold;")
        layout.addWidget(self.best_ask_label, 1, 1)
        
        # Spread
        layout.addWidget(QLabel("Spread:"), 2, 0)
        self.spread_label = QLabel("--")
        self.spread_label.setStyleSheet("color: #0066CC; font-weight: bold;")
        layout.addWidget(self.spread_label, 2, 1)
        
        parent_layout.addWidget(group)
    
    def create_trading_buttons_section(self, parent_layout):
        """Create trading buttons section"""
        group = QGroupBox("Execute Trades")
        layout = QGridLayout(group)
        layout.setSpacing(8)
        
        # Buy buttons (green)
        self.limit_buy_btn = QPushButton("LIMIT BUY")
        self.limit_buy_btn.setStyleSheet("""
            QPushButton {
                background-color: #006600;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #008800;
            }
            QPushButton:pressed {
                background-color: #004400;
            }
        """)
        self.limit_buy_btn.clicked.connect(self.place_limit_buy)
        layout.addWidget(self.limit_buy_btn, 0, 0)
        
        self.market_buy_btn = QPushButton("MARKET BUY")
        self.market_buy_btn.setStyleSheet("""
            QPushButton {
                background-color: #009900;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #00BB00;
            }
            QPushButton:pressed {
                background-color: #006600;
            }
        """)
        self.market_buy_btn.clicked.connect(self.place_market_buy)
        layout.addWidget(self.market_buy_btn, 0, 1)
        
        # Sell buttons (red)
        self.limit_sell_btn = QPushButton("LIMIT SELL")
        self.limit_sell_btn.setStyleSheet("""
            QPushButton {
                background-color: #660000;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #880000;
            }
            QPushButton:pressed {
                background-color: #440000;
            }
        """)
        self.limit_sell_btn.clicked.connect(self.place_limit_sell)
        layout.addWidget(self.limit_sell_btn, 1, 0)
        
        self.market_sell_btn = QPushButton("MARKET SELL")
        self.market_sell_btn.setStyleSheet("""
            QPushButton {
                background-color: #990000;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #BB0000;
            }
            QPushButton:pressed {
                background-color: #660000;
            }
        """)
        self.market_sell_btn.clicked.connect(self.place_market_sell)
        layout.addWidget(self.market_sell_btn, 1, 1)
        
        parent_layout.addWidget(group)
    
    def create_position_management_section(self, parent_layout):
        """Create position management section"""
        group = QGroupBox("Position Management")
        layout = QGridLayout(group)
        layout.setSpacing(8)
        
        # Close position buttons
        self.limit_close_btn = QPushButton("LIMIT CLOSE")
        self.limit_close_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF6600;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #FF8800;
            }
        """)
        self.limit_close_btn.clicked.connect(self.place_limit_close)
        layout.addWidget(self.limit_close_btn, 0, 0)
        
        self.market_close_btn = QPushButton("MARKET CLOSE")
        self.market_close_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF8800;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #FFAA00;
            }
        """)
        self.market_close_btn.clicked.connect(self.place_market_close)
        layout.addWidget(self.market_close_btn, 0, 1)
        
        # Control buttons
        self.close_all_btn = QPushButton("CLOSE ALL POSITIONS")
        self.close_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #CC4400;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #EE6600;
            }
        """)
        self.close_all_btn.clicked.connect(self.close_all_positions)
        layout.addWidget(self.close_all_btn, 1, 0)
        
        self.cancel_all_btn = QPushButton("CANCEL ALL ORDERS")
        self.cancel_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #666666;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #888888;
            }
        """)
        self.cancel_all_btn.clicked.connect(self.cancel_all_orders)
        layout.addWidget(self.cancel_all_btn, 1, 1)
        
        parent_layout.addWidget(group)
    
    def create_account_info_section(self, parent_layout):
        """Create account information section"""
        group = QGroupBox("Account Information")
        layout = QGridLayout(group)
        layout.setSpacing(5)
        
        # Balance
        layout.addWidget(QLabel("Balance:"), 0, 0)
        self.balance_label = QLabel("--")
        self.balance_label.setStyleSheet("color: #0066CC; font-weight: bold;")
        layout.addWidget(self.balance_label, 0, 1)
        
        # Open Positions
        layout.addWidget(QLabel("Positions:"), 1, 0)
        self.positions_label = QLabel("--")
        self.positions_label.setStyleSheet("color: #0066CC; font-weight: bold;")
        layout.addWidget(self.positions_label, 1, 1)
        
        # Pending Orders
        layout.addWidget(QLabel("Orders:"), 2, 0)
        self.orders_label = QLabel("--")
        self.orders_label.setStyleSheet("color: #0066CC; font-weight: bold;")
        layout.addWidget(self.orders_label, 2, 1)
        
        parent_layout.addWidget(group)
    
    def setup_styles(self):
        """Setup window styles"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #CCCCCC;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2E86AB;
            }
            QLabel {
                color: #333333;
                font-size: 11px;
            }
            QComboBox, QDoubleSpinBox, QSpinBox {
                padding: 5px;
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                background-color: white;
                font-size: 11px;
            }
        """)
    
    # Trading Methods - delegate to main interface
    def place_limit_buy(self):
        """Place limit buy order"""
        if self.main_interface:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            price = self.price_spinbox.value() if self.price_spinbox.value() > 0 else None
            success = self.main_interface.place_limit_buy(symbol, quantity, price)
            if success:
                self.trade_executed.emit(symbol, "BUY", quantity, price or 0, success, 0.0)
    
    def place_market_buy(self):
        """Place market buy order"""
        if self.main_interface:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            success = self.main_interface.place_market_buy(symbol, quantity)
            if success:
                self.trade_executed.emit(symbol, "BUY", quantity, 0, success, 0.0)
    
    def place_limit_sell(self):
        """Place limit sell order"""
        if self.main_interface:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            price = self.price_spinbox.value() if self.price_spinbox.value() > 0 else None
            success = self.main_interface.place_limit_sell(symbol, quantity, price)
            if success:
                self.trade_executed.emit(symbol, "SELL", quantity, price or 0, success, 0.0)
    
    def place_market_sell(self):
        """Place market sell order"""
        if self.main_interface:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            success = self.main_interface.place_market_sell(symbol, quantity)
            if success:
                self.trade_executed.emit(symbol, "SELL", quantity, 0, success, 0.0)
    
    def place_limit_close(self):
        """Place limit close order"""
        if self.main_interface:
            symbol = self.symbol_combo.currentText()
            price = self.price_spinbox.value() if self.price_spinbox.value() > 0 else None
            success = self.main_interface.place_limit_close(symbol, None, price)
            if success:
                self.position_closed.emit(symbol, success)
    
    def place_market_close(self):
        """Place market close order"""
        if self.main_interface:
            symbol = self.symbol_combo.currentText()
            success = self.main_interface.place_market_close(symbol)
            if success:
                self.position_closed.emit(symbol, success)
    
    def close_all_positions(self):
        """Close all positions"""
        if self.main_interface:
            success = self.main_interface.close_all_positions()
            self.position_closed.emit("ALL", success)
    
    def cancel_all_orders(self):
        """Cancel all orders"""
        if self.main_interface:
            success = self.main_interface.cancel_all_orders()
            self.orders_cancelled.emit(success)
    
    # Event handlers
    def on_symbol_changed(self, symbol):
        """Handle symbol change"""
        if self.main_interface and hasattr(self.main_interface, 'symbol_combo'):
            # Sync with main interface
            self.main_interface.symbol_combo.setCurrentText(symbol)
    
    def on_leverage_changed(self, leverage):
        """Handle leverage change"""
        if self.main_interface and hasattr(self.main_interface, 'on_leverage_changed'):
            self.main_interface.on_leverage_changed(leverage)
    
    def update_displays(self):
        """Update real-time displays"""
        try:
            if not self.main_interface:
                return
            
            # Update market data
            if hasattr(self.main_interface, 'current_bid') and hasattr(self.main_interface, 'current_ask'):
                bid = getattr(self.main_interface, 'current_bid', None)
                ask = getattr(self.main_interface, 'current_ask', None)
                
                if bid is not None:
                    self.best_bid_label.setText(f"{bid:.6f}")
                if ask is not None:
                    self.best_ask_label.setText(f"{ask:.6f}")
                
                if bid is not None and ask is not None:
                    spread = ask - bid
                    self.spread_label.setText(f"{spread:.6f}")
            
            # Update account info
            if hasattr(self.main_interface, 'real_trading') and self.main_interface.real_trading:
                try:
                    balance_info = self.main_interface.real_trading.get_balance_info()
                    if balance_info and 'USDT' in balance_info:
                        balance = balance_info['USDT'].get('free', 0)
                        self.balance_label.setText(f"${balance:.2f}")
                except:
                    pass
            
            # Update positions and orders count
            if hasattr(self.main_interface, 'positions_data'):
                pos_count = len(getattr(self.main_interface, 'positions_data', []))
                self.positions_label.setText(str(pos_count))
            
            if hasattr(self.main_interface, 'orders_data'):
                orders_count = len(getattr(self.main_interface, 'orders_data', []))
                self.orders_label.setText(str(orders_count))
                
        except Exception as e:
            logger.error(f"Error updating displays: {e}")

# Test the window standalone
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ManualTradingWindow()
    window.show()
    sys.exit(app.exec_())
