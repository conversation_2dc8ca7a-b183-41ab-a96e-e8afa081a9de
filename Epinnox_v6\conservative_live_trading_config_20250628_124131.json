{"system_info": {"config_version": "1.0.0", "created_timestamp": "2025-06-28T12:41:31.706054", "deployment_type": "conservative_live", "description": "Ultra-conservative configuration for initial live trading deployment"}, "risk_management": {"max_portfolio_risk": 0.02, "max_position_size": 0.01, "max_leverage": 1.0, "max_daily_loss": 0.005, "max_concurrent_positions": 1, "position_timeout_hours": 24, "stop_loss_percentage": 0.02, "take_profit_percentage": 0.04}, "trading_parameters": {"initial_balance": 10.0, "minimum_confidence": 0.8, "trading_symbols": ["BTC/USDT:USDT"], "trading_hours": {"enabled": true, "start_hour": 9, "end_hour": 17}, "cooldown_period_minutes": 60, "max_trades_per_day": 2}, "ai_model_settings": {"rl_agent": {"enabled": true, "confidence_threshold": 0.75, "exploration_rate": 0.05}, "llm_analysis": {"enabled": true, "minimum_confidence": 0.8, "sentiment_weight": 0.3, "technical_weight": 0.7}, "decision_aggregation": {"require_consensus": true, "minimum_combined_confidence": 0.85}}, "monitoring_and_alerts": {"real_time_monitoring": {"enabled": true, "update_interval_seconds": 30, "log_all_decisions": true}, "alert_thresholds": {"portfolio_loss_percent": 0.5, "position_loss_percent": 1.0, "system_error_count": 3, "api_failure_count": 2}, "notification_methods": {"console_logging": true, "file_logging": true, "email_alerts": false, "webhook_alerts": false}}, "safety_mechanisms": {"circuit_breakers": {"daily_loss_limit": 0.005, "consecutive_losses": 3, "api_error_limit": 5, "system_error_limit": 3}, "emergency_stop": {"enabled": true, "manual_override": true, "auto_stop_conditions": ["daily_loss_exceeded", "consecutive_losses_exceeded", "system_errors_exceeded"]}, "position_management": {"force_close_on_loss": true, "force_close_percentage": 2.0, "trailing_stop_enabled": false, "partial_close_enabled": false}}, "exchange_settings": {"primary_exchange": "htx", "demo_mode": false, "api_rate_limits": {"requests_per_minute": 30, "orders_per_minute": 5}, "order_types": {"market_orders": true, "limit_orders": false, "stop_orders": true, "advanced_orders": false}}, "logging_and_reporting": {"detailed_logging": true, "log_level": "INFO", "log_file_rotation": true, "daily_reports": true, "trade_history_backup": true, "performance_metrics": {"track_pnl": true, "track_drawdown": true, "track_win_rate": true, "track_sharpe_ratio": true}}}