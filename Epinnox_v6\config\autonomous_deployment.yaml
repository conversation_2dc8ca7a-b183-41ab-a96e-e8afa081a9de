# Epinnox v6 Autonomous Trading Deployment Configuration
# Ultra-Conservative Settings for Initial Live Deployment
# Created: 2025-06-29
# Target: Fully autonomous operation with minimal human intervention

system:
  deployment_type: "autonomous_live"
  version: "6.0.0"
  created: "2025-06-29"
  description: "Ultra-conservative autonomous trading configuration"
  
# ULTRA-CONSERVATIVE RISK MANAGEMENT
risk_management:
  # Portfolio-level limits
  max_portfolio_risk: 0.02              # 2% maximum portfolio risk
  portfolio_exposure_limit: 0.80        # 80% exposure limit (enforced)
  max_daily_loss: 0.02                  # 2% max daily loss
  max_drawdown: 0.05                    # 5% maximum drawdown
  
  # Position-level limits
  max_position_size: 0.01               # 1% max per position
  max_concurrent_positions: 1           # Only 1 position at a time
  max_leverage: 2.0                     # Conservative 2x leverage max
  
  # Trade execution limits
  stop_loss_pct: 0.015                 # 1.5% stop loss
  take_profit_pct: 0.03                # 3% take profit
  max_trades_per_day: 3                # Maximum 3 trades per day
  cooldown_minutes: 120                # 2-hour cooldown between trades
  
  # Emergency circuit breakers
  circuit_breakers:
    consecutive_losses: 2               # Stop after 2 consecutive losses
    daily_loss_threshold: 0.015        # Emergency stop at 1.5% daily loss
    api_error_limit: 3                 # Stop after 3 API errors
    system_error_limit: 2              # Stop after 2 system errors

# AUTONOMOUS OPERATION SETTINGS
autonomous:
  enabled: true                         # Enable full autonomous mode
  human_intervention_required: false    # No human intervention needed
  
  # Decision making
  min_confidence_threshold: 0.80       # 80% minimum confidence for trades
  require_llm_consensus: true          # Require LLM consensus for trades
  min_combined_confidence: 0.85        # 85% combined confidence threshold
  
  # Operational parameters
  cycle_delay_seconds: 60              # 60-second analysis cycles
  position_timeout_hours: 24           # Close positions after 24 hours
  health_check_interval: 30            # Health check every 30 seconds
  
  # Safety monitoring
  continuous_monitoring: true          # 24/7 monitoring enabled
  auto_risk_adjustment: true           # Automatically adjust risk based on performance
  emergency_stop_enabled: true        # Emergency stop functionality active

# TRADING PARAMETERS
trading:
  initial_balance: 50.0                # Start with $50
  symbols: ["DOGE/USDT:USDT"]         # Single symbol for focused trading
  exchange: "htx"                      # HTX exchange
  demo_mode: false                     # Live trading mode
  
  # Order management
  order_types:
    market_orders: true                # Enable market orders
    limit_orders: true                 # Enable limit orders
    stop_orders: true                  # Enable stop orders
  
  # Timing controls
  trading_hours:
    enabled: false                     # 24/7 trading
    start_hour: 0
    end_hour: 24
  
  # Position management
  force_close_on_timeout: true        # Auto-close positions after timeout
  partial_close_enabled: false        # No partial closes for simplicity
  trailing_stop_enabled: false        # No trailing stops initially

# AI/ML CONFIGURATION
ai_models:
  llm_orchestrator:
    enabled: true                      # LLM analysis enabled
    model: "phi-3.1-mini-128k-instruct"
    prompts_required: 3                # Minimum 3 prompts for decision
    min_prompt_confidence: 0.75        # 75% minimum per prompt
    
  ensemble_models:
    enabled: true                      # ML ensemble enabled
    min_model_agreement: 0.70          # 70% model agreement required
    confidence_weighting: true         # Weight by confidence
    
  decision_aggregation:
    method: "weighted_consensus"       # Use weighted consensus
    llm_weight: 0.60                  # 60% weight to LLM
    ml_weight: 0.40                   # 40% weight to ML models

# MONITORING AND ALERTING
monitoring:
  real_time_monitoring: true          # Real-time monitoring enabled
  update_interval_seconds: 30         # Update every 30 seconds
  log_all_decisions: true             # Log every decision
  
  # Performance tracking
  track_performance: true             # Track all performance metrics
  save_trade_history: true            # Save complete trade history
  generate_reports: true              # Generate performance reports
  
  # Alert thresholds
  alerts:
    portfolio_loss_pct: 0.01          # Alert at 1% portfolio loss
    position_loss_pct: 0.015          # Alert at 1.5% position loss
    consecutive_losses: 2              # Alert after 2 consecutive losses
    api_failures: 2                   # Alert after 2 API failures
  
  # Notification methods
  notifications:
    console_logging: true             # Console notifications
    file_logging: true                # File logging
    database_logging: true            # Database logging
    email_alerts: false               # No email alerts initially
    webhook_alerts: false             # No webhook alerts initially

# SAFETY MECHANISMS
safety:
  # Automated safety checks
  pre_trade_validation: true          # Validate every trade before execution
  post_trade_monitoring: true         # Monitor trades after execution
  continuous_risk_assessment: true    # Continuous risk monitoring
  
  # Emergency procedures
  emergency_stop:
    manual_trigger: true              # Manual emergency stop available
    auto_trigger_conditions:
      - "daily_loss_exceeded"
      - "consecutive_losses_exceeded"
      - "system_errors_exceeded"
      - "api_errors_exceeded"
  
  # Recovery procedures
  auto_recovery:
    enabled: true                     # Auto-recovery enabled
    recovery_delay_minutes: 60        # Wait 1 hour before recovery attempt
    max_recovery_attempts: 3          # Maximum 3 recovery attempts
    
# DEPLOYMENT VALIDATION
validation:
  required_checks:
    - "api_connectivity"              # API connection working
    - "balance_sufficient"            # Sufficient balance available
    - "risk_limits_configured"        # Risk limits properly set
    - "safety_mechanisms_active"      # Safety mechanisms operational
    - "monitoring_systems_ready"      # Monitoring systems ready
  
  pre_deployment_tests:
    - "paper_trading_validation"      # Paper trading test required
    - "risk_limit_enforcement"        # Risk limit enforcement test
    - "emergency_stop_test"           # Emergency stop functionality test
    - "autonomous_decision_cycle"     # Full autonomous cycle test

# SCALING CRITERIA
scaling:
  # Performance thresholds for scaling up
  scale_up_criteria:
    min_trades_completed: 50          # Minimum 50 successful trades
    min_win_rate: 0.60               # 60% minimum win rate
    max_drawdown_observed: 0.03      # Maximum 3% drawdown observed
    min_days_operational: 14         # Minimum 14 days operational
    
  # Scaling increments
  balance_scaling:
    next_level: 100.0                # Scale to $100 next
    increment_pct: 1.0               # 100% increment (double)
    max_balance: 1000.0              # Maximum $1000 for this phase
    
  # Risk scaling (gradually increase limits)
  risk_scaling:
    position_size_increment: 0.005   # Increase position size by 0.5%
    max_position_size_scaled: 0.02   # Maximum 2% position size when scaled
    max_concurrent_positions_scaled: 2 # Maximum 2 positions when scaled
