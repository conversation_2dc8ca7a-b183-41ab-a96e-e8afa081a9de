#!/usr/bin/env python3
"""
🚀 EPINNOX LIVE TRADING LAUNCHER
Execute autonomous trading with real money using ultra-conservative settings
"""

import argparse
import sys
import os
import asyncio
import signal
import json
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import credentials and trading components
try:
    import credentials
    from credentials import (
        HTX_API_KEY, HTX_SECRET_KEY, ACCOUNT_NAME, CONSERVATIVE_SETTINGS,
        validate_credentials, get_account_info
    )
except ImportError as e:
    print(f"❌ Failed to import credentials: {e}")
    sys.exit(1)

from trading.ccxt_trading_engine import CCXTTradingEngine
from portfolio.portfolio_manager import PortfolioManager
from execution.autonomous_executor import AutonomousTradeExecutor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'live_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveTradingSession:
    """Live trading session manager with real money"""
    
    def __init__(self, config):
        self.config = config
        self.running = False
        self.exchange_engine = None
        self.portfolio_manager = None
        self.autonomous_executor = None
        self.start_time = None
        self.trade_count = 0
        self.last_trade_time = None
        
    async def initialize(self):
        """Initialize live trading components"""
        logger.info("🚀 Initializing LIVE trading session...")
        logger.info(f"   Account: {ACCOUNT_NAME}")
        logger.info(f"   Initial Balance: ${self.config['initial_balance']:.2f}")
        logger.info(f"   Max Position Size: {self.config['max_position_size']:.1%}")
        logger.info(f"   Daily Loss Limit: {self.config['max_daily_loss']:.1%}")
        
        # Validate credentials
        if not validate_credentials():
            raise Exception("Invalid credentials - cannot start live trading")
        
        # Initialize exchange connection (LIVE MODE)
        self.exchange_engine = CCXTTradingEngine('htx', demo_mode=False)
        if not self.exchange_engine.initialize_exchange():
            raise Exception("Failed to connect to HTX exchange in live mode")
        
        logger.info("✅ HTX exchange connected in LIVE MODE")
        
        # Verify account balance
        balance = self.exchange_engine.exchange.fetch_balance()
        usdt_balance = balance.get('USDT', {}).get('free', 0)
        logger.info(f"✅ Account balance: ${usdt_balance:.2f} USDT")
        
        if usdt_balance < self.config['initial_balance']:
            logger.warning(f"⚠️ Account balance ${usdt_balance:.2f} less than configured ${self.config['initial_balance']:.2f}")
        
        # Initialize portfolio manager with conservative settings
        self.portfolio_manager = PortfolioManager(
            initial_balance=self.config['initial_balance'], 
            max_positions=1
        )
        
        # Set ultra-conservative risk limits
        self.portfolio_manager.set_risk_limits(
            max_portfolio_risk=self.config['max_portfolio_risk'],
            max_position_size=self.config['max_position_size'],
            max_leverage=self.config['max_leverage'],
            max_daily_loss=self.config['max_daily_loss']
        )
        
        # Initialize autonomous executor
        self.autonomous_executor = AutonomousTradeExecutor(self.exchange_engine.exchange)
        
        logger.info("✅ Live trading components initialized")
        
    def mock_llm_decision(self, symbol, market_data):
        """Mock LLM decision for live trading"""
        import random
        import time
        
        # Simulate LLM processing
        time.sleep(random.uniform(1.0, 2.0))
        
        price_change = market_data.get('price_change_24h', 0.0)
        volatility = market_data.get('volatility', 0.02)
        
        # Ultra-conservative decision making
        if abs(price_change) < 0.01 and volatility < 0.02:
            decision = 'WAIT'
            confidence = 0.6 + random.uniform(0, 0.2)
        elif price_change > 0.04 and volatility < 0.03:  # Very strong upward, low volatility
            decision = 'LONG'
            confidence = 0.8 + random.uniform(0, 0.15)
        elif price_change < -0.04 and volatility < 0.03:  # Very strong downward, low volatility
            decision = 'SHORT'
            confidence = 0.8 + random.uniform(0, 0.15)
        else:
            decision = 'WAIT'  # Conservative default
            confidence = 0.5 + random.uniform(0, 0.3)
        
        return {
            'decision': decision,
            'confidence': min(0.95, max(0.1, confidence)),
            'reasoning': f"Conservative LLM analysis for {symbol}: {decision} based on price change {price_change:.2%}, volatility {volatility:.2%}"
        }
    
    async def execute_trading_cycle(self):
        """Execute one trading cycle with real money"""
        symbol = self.config['trading_symbol']
        
        try:
            logger.info(f"🔄 Starting live trading cycle for {symbol}")
            
            # Fetch real market data
            ticker = self.exchange_engine.exchange.fetch_ticker(symbol)
            ohlcv = self.exchange_engine.exchange.fetch_ohlcv(symbol, '1m', limit=100)
            
            current_price = ticker['last']
            price_change_24h = ticker['percentage'] / 100 if ticker['percentage'] else 0.0
            volume_24h = ticker['quoteVolume'] if ticker['quoteVolume'] else 1000000
            
            # Calculate volatility
            if len(ohlcv) >= 20:
                prices = [candle[4] for candle in ohlcv[-20:]]
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
            else:
                volatility = 0.02
            
            market_data = {
                'current_price': current_price,
                'price_change_24h': price_change_24h,
                'volume_24h': volume_24h,
                'volatility': volatility
            }
            
            logger.info(f"📊 Market: ${current_price:.2f}, Change: {price_change_24h:.2%}, Vol: {volatility:.2%}")
            
            # LLM Decision Making
            llm_decision = self.mock_llm_decision(symbol, market_data)
            logger.info(f"🧠 LLM: {llm_decision['decision']} ({llm_decision['confidence']:.1%} confidence)")
            
            # Risk Assessment
            if llm_decision['confidence'] < self.config['minimum_confidence']:
                logger.info(f"⚠️ Confidence {llm_decision['confidence']:.1%} below {self.config['minimum_confidence']:.1%} threshold")
                return False
            
            # Check daily trade limit
            if self.trade_count >= self.config['max_trades_per_day']:
                logger.info(f"⚠️ Daily trade limit reached ({self.trade_count}/{self.config['max_trades_per_day']})")
                return False
            
            # Check cooldown period
            if self.last_trade_time:
                time_since_last = datetime.now() - self.last_trade_time
                cooldown_minutes = self.config['cooldown_minutes']
                if time_since_last.total_seconds() < cooldown_minutes * 60:
                    remaining = cooldown_minutes * 60 - time_since_last.total_seconds()
                    logger.info(f"⏱️ Cooldown: {remaining/60:.1f} minutes remaining")
                    return False
            
            # Execute trade if decision is not WAIT
            if llm_decision['decision'] != 'WAIT':
                position_size_pct = self.config['max_position_size']
                position_value = self.portfolio_manager.current_balance * position_size_pct
                position_quantity = position_value / current_price
                
                # Check if we can open position
                can_trade = await self.portfolio_manager.can_open_position(symbol, position_value, 1.0)
                
                if can_trade['allowed']:
                    side = 'long' if llm_decision['decision'] == 'LONG' else 'short'
                    
                    # Execute REAL trade
                    await self.portfolio_manager.open_position(
                        symbol, side, position_quantity, current_price, 1.0,
                        stop_loss=current_price * (0.98 if side == 'long' else 1.02),
                        take_profit=current_price * (1.04 if side == 'long' else 0.96)
                    )
                    
                    self.trade_count += 1
                    self.last_trade_time = datetime.now()
                    
                    logger.info(f"✅ LIVE TRADE: {side.upper()} {position_quantity:.6f} {symbol} @ ${current_price:.2f}")
                    logger.info(f"   Position value: ${position_value:.2f} ({position_size_pct:.1%} of balance)")
                    logger.info(f"   Trade #{self.trade_count} of {self.config['max_trades_per_day']} daily limit")
                    
                    return True
                else:
                    logger.warning(f"⚠️ Trade blocked: {can_trade['reason']}")
                    return False
            else:
                logger.info("💤 Decision: WAIT - No trade executed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Trading cycle failed: {e}")
            return False
    
    async def run_session(self, duration_minutes=None):
        """Run live trading session"""
        self.running = True
        self.start_time = datetime.now()
        
        if duration_minutes:
            end_time = self.start_time + timedelta(minutes=duration_minutes)
            logger.info(f"🕐 Session will run for {duration_minutes} minutes")
        else:
            end_time = None
            logger.info("🕐 Session will run indefinitely (Ctrl+C to stop)")
        
        cycle_count = 0
        
        while self.running:
            if end_time and datetime.now() >= end_time:
                logger.info("⏰ Session duration reached")
                break
            
            cycle_count += 1
            logger.info(f"\n{'='*60}")
            logger.info(f"🔄 Live Trading Cycle #{cycle_count}")
            logger.info(f"{'='*60}")
            
            # Execute trading cycle
            await self.execute_trading_cycle()
            
            # Log session status
            elapsed = datetime.now() - self.start_time
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            
            logger.info(f"📊 Session Status:")
            logger.info(f"   • Elapsed: {elapsed}")
            logger.info(f"   • Trades: {self.trade_count}")
            logger.info(f"   • Balance: ${portfolio_summary['current_balance']:.2f}")
            logger.info(f"   • Total Value: ${portfolio_summary['total_value']:.2f}")
            
            # Wait for next cycle
            cooldown = self.config['cycle_delay']
            logger.info(f"⏱️ Waiting {cooldown} seconds until next cycle...")
            
            for i in range(cooldown):
                if not self.running:
                    break
                await asyncio.sleep(1)
        
        logger.info("🏁 Live trading session completed")
    
    def stop(self):
        """Stop the trading session"""
        self.running = False
        logger.info("🛑 Stopping live trading session...")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Epinnox Live Trading System')
    parser.add_argument('--balance', type=float, default=10.0,
                       help='Initial balance for trading (default: 10.0)')
    parser.add_argument('--duration', type=int,
                       help='Session duration in minutes')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Display startup banner
    print("🚀" + "="*58 + "🚀")
    print("🚀" + " "*15 + "EPINNOX LIVE TRADING SYSTEM" + " "*15 + "🚀")
    print("🚀" + "="*58 + "🚀")
    print()
    print(f"📋 Configuration:")
    print(f"   • Account: {ACCOUNT_NAME}")
    print(f"   • Initial Balance: ${args.balance:.2f}")
    print(f"   • Max Position: {CONSERVATIVE_SETTINGS['max_position_size']:.1%}")
    print(f"   • Daily Loss Limit: {CONSERVATIVE_SETTINGS['max_daily_loss']:.1%}")
    print(f"   • Min Confidence: {CONSERVATIVE_SETTINGS['minimum_confidence']:.0%}")
    print()
    print("🛡️ SAFETY PROTOCOLS ACTIVE")
    print("   • Emergency Stop: Ctrl+C")
    print("   • Circuit Breakers: Active")
    print("   • Risk Limits: Enforced")
    print()
    
    # Create configuration
    config = {
        'initial_balance': args.balance,
        'max_portfolio_risk': CONSERVATIVE_SETTINGS['max_portfolio_risk'],
        'max_position_size': CONSERVATIVE_SETTINGS['max_position_size'],
        'max_leverage': CONSERVATIVE_SETTINGS['max_leverage'],
        'max_daily_loss': CONSERVATIVE_SETTINGS['max_daily_loss'],
        'minimum_confidence': CONSERVATIVE_SETTINGS['minimum_confidence'],
        'max_trades_per_day': CONSERVATIVE_SETTINGS['max_trades_per_day'],
        'trading_symbol': CONSERVATIVE_SETTINGS['trading_symbol'],
        'cooldown_minutes': CONSERVATIVE_SETTINGS['cooldown_minutes'],
        'cycle_delay': 60  # 60 seconds between cycles
    }
    
    # Create live trading session
    session = LiveTradingSession(config)
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info("🛑 Received shutdown signal")
        session.stop()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(run_live_trading_session(session, args.duration))
    except KeyboardInterrupt:
        logger.info("🛑 Live trading interrupted by user")
    except Exception as e:
        logger.error(f"❌ Live trading failed: {e}")
        return 1
    
    return 0

async def run_live_trading_session(session, duration):
    """Run the live trading session"""
    await session.initialize()
    await session.run_session(duration)

if __name__ == "__main__":
    sys.exit(main())
