["test_imports.py::test_basic_functionality", "test_imports.py::test_imports", "test_system_integration.py::test_autonomous_orchestrator", "test_system_integration.py::test_data_manager", "test_system_integration.py::test_llm_orchestrator", "test_system_integration.py::test_portfolio_manager", "test_system_integration.py::test_unicode_logging", "tests/gpu/test_gpu.py::test_cuda_availability", "tests/gpu/test_gpu.py::test_matrix_multiplication", "tests/gpu/test_gpu.py::test_model_loading", "tests/models/test_mistral.py::test_mistral", "tests/models/test_model_loader.py::test_phi2", "tests/models/test_model_loader.py::test_tinyllama", "tests/models/test_tinyllama.py::test_tinyllama", "tests/models/test_transformers_runner.py::test_transformers_models", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_order_success", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_order_with_futures_symbol", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_high_confidence", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_low_confidence", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_execute_trading_decision_wait", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_get_account_balance", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_missing_position_sizing_data", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_pre_execution_risk_check_excessive_leverage", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_pre_execution_risk_check_large_position", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_setup_risk_orders", "tests/test_autonomous_executor.py::TestAutonomousTradeExecutor::test_trade_order_creation", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_autonomous_controller_initialization", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_decision_to_execution_flow", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_error_handling_integration", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_full_trading_cycle", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_performance_tracking_integration", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_portfolio_position_updates", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_risk_management_integration", "tests/test_autonomous_integration.py::TestAutonomousSystemIntegration::test_stop_loss_integration", "tests/test_autonomous_system.py::TestAutonomousTradingOrchestrator::test_emergency_stop", "tests/test_autonomous_system.py::TestAutonomousTradingOrchestrator::test_initialization", "tests/test_autonomous_system.py::TestAutonomousTradingOrchestrator::test_initialization_failure", "tests/test_autonomous_system.py::TestAutonomousTradingOrchestrator::test_initialization_process", "tests/test_autonomous_system.py::TestAutonomousTradingOrchestrator::test_trading_decision_creation", "tests/test_autonomous_system.py::TestEnhancedLLMAnalyzer::test_basic_indicators_calculation", "tests/test_autonomous_system.py::TestEnhancedLLMAnalyzer::test_fallback_analysis", "tests/test_autonomous_system.py::TestEnhancedLLMAnalyzer::test_initialization", "tests/test_autonomous_system.py::TestEnhancedLLMAnalyzer::test_market_analysis_with_mock_llm", "tests/test_autonomous_system.py::TestEnhancedLLMAnalyzer::test_market_context_preparation", "tests/test_autonomous_system.py::TestSafetyMonitor::test_account_balance_alert", "tests/test_autonomous_system.py::TestSafetyMonitor::test_daily_loss_alert", "tests/test_autonomous_system.py::TestSafetyMonitor::test_emergency_callback_registration", "tests/test_autonomous_system.py::TestSafetyMonitor::test_initialization", "tests/test_autonomous_system.py::TestSafetyMonitor::test_safety_rules_initialization", "tests/test_autonomous_system.py::TestSafetyMonitor::test_system_status", "tests/test_autonomous_system.py::TestSimulationExecutor::test_buy_order_execution", "tests/test_autonomous_system.py::TestSimulationExecutor::test_initialization", "tests/test_autonomous_system.py::TestSimulationExecutor::test_insufficient_balance", "tests/test_autonomous_system.py::TestSimulationExecutor::test_performance_summary", "tests/test_autonomous_system.py::TestSimulationExecutor::test_position_tracking", "tests/test_enhanced_integration.py::test_gpu_monitor", "tests/test_enhanced_integration.py::test_model_cache", "tests/test_enhanced_integration.py::test_transformers_runner", "tests/test_exchange.py::TestExchangeDataFetcher::test_caching", "tests/test_exchange.py::TestExchangeDataFetcher::test_fetch_ohlcv", "tests/test_exchange.py::TestExchangeDataFetcher::test_fetch_order_book", "tests/test_exchange.py::TestExchangeDataFetcher::test_fetch_trades", "tests/test_gui_live_integration.py::TestLiveSystemErrorHandling::test_portfolio_overexposure_prevention", "tests/test_gui_live_integration.py::TestLiveSystemErrorHandling::test_websocket_disconnection_handling", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_account_balance_display", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_emergency_stop_accessibility", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_leverage_mismatch_error_display", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_llm_orchestrator_results_display", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_portfolio_exposure_warning_display", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_position_display_integration", "tests/test_gui_live_integration.py::TestLiveSystemGUIIntegration::test_real_time_data_integration", "tests/test_gui_live_integration.py::TestLiveSystemValidation::test_session_tracking_integration", "tests/test_gui_live_integration.py::TestLiveSystemValidation::test_system_initialization_validation", "tests/test_indicators.py::TestIndicators::test_analyze_trend", "tests/test_indicators.py::TestIndicators::test_calculate_indicators", "tests/test_indicators.py::TestIndicators::test_calculate_trend_strength", "tests/test_indicators.py::TestIndicators::test_extract_features", "tests/test_indicators.py::TestIndicators::test_signal_scorer", "tests/test_integration.py::TestIntegration::test_end_to_end_workflow", "tests/test_integration.py::TestIntegration::test_simulation_trader", "tests/test_integration_workflow.py::TestIntegrationWorkflow::test_complete_autonomous_simulation", "tests/test_integration_workflow.py::TestIntegrationWorkflow::test_data_flow_pipeline", "tests/test_integration_workflow.py::TestIntegrationWorkflow::test_error_recovery_workflow", "tests/test_integration_workflow.py::TestIntegrationWorkflow::test_orchestrator_integration_readiness", "tests/test_integration_workflow.py::TestIntegrationWorkflow::test_position_management_workflow", "tests/test_integration_workflow.py::TestIntegrationWorkflow::test_trading_execution_workflow", "tests/test_leverage_system.py::test_leverage_system", "tests/test_live_data.py::test_live_data", "tests/test_llm_orchestrator_harness.py::test_cache_growth_and_teardown", "tests/test_llm_orchestrator_harness.py::test_enters_action_zone_triggers_llm", "tests/test_llm_orchestrator_harness.py::test_llm_decision_aggregation[responses0-LONG]", "tests/test_llm_orchestrator_harness.py::test_llm_decision_aggregation[responses1-SHORT]", "tests/test_llm_orchestrator_harness.py::test_outside_action_zone_waits", "tests/test_optimizations.py::test_optimizations", "tests/test_optimizations.py::test_position_sizing_standalone", "tests/test_optimizations.py::test_signal_hierarchy_standalone", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_daily_metrics_empty", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_daily_metrics_with_trades", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_max_drawdown", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_profit_factor", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_profit_factor_no_losses", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_sharpe_ratio", "tests/test_performance_tracker.py::TestPerformanceTracker::test_calculate_sharpe_ratio_empty", "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_empty", "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_model_performance_with_data", "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary", "tests/test_performance_tracker.py::TestPerformanceTracker::test_get_recent_performance_summary_empty", "tests/test_performance_tracker.py::TestPerformanceTracker::test_init_database", "tests/test_performance_tracker.py::TestPerformanceTracker::test_record_trade", "tests/test_performance_tracker.py::TestPerformanceTracker::test_save_daily_metrics", "tests/test_portfolio_manager.py::TestPortfolioManager::test_calculate_total_portfolio_risk", "tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_excessive_leverage", "tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_excessive_size", "tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_max_positions", "tests/test_portfolio_manager.py::TestPortfolioManager::test_can_open_position_success", "tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_stop_loss", "tests/test_portfolio_manager.py::TestPortfolioManager::test_check_risk_levels_take_profit", "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_nonexistent_position", "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_long_profit", "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_loss", "tests/test_portfolio_manager.py::TestPortfolioManager::test_close_position_short_profit", "tests/test_portfolio_manager.py::TestPortfolioManager::test_get_portfolio_summary", "tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_success", "tests/test_portfolio_manager.py::TestPortfolioManager::test_open_position_switch_sides", "tests/test_portfolio_manager.py::TestPortfolioManager::test_reset_daily_pnl", "tests/test_portfolio_manager.py::TestPortfolioManager::test_set_risk_limits", "tests/test_portfolio_manager.py::TestPortfolioManager::test_update_positions", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_data_refresh_method_existence", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_data_refresh_methods_execution", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_error_handling_robustness", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_orchestrator_integration_readiness", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_position_management_safety", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_trading_method_existence", "tests/test_real_interface_operations.py::TestRealInterfaceOperations::test_trading_methods_safe_execution", "tests/test_reorganized_ui.py::test_reorganized_ui", "tests/test_rl_agent.py::TestTradingCallback::test_callback_initialization", "tests/test_rl_agent.py::TestTradingCallback::test_callback_on_step", "tests/test_rl_agent.py::TestTradingEnvironment::test_environment_initialization", "tests/test_rl_agent.py::TestTradingEnvironment::test_episode_termination", "tests/test_rl_agent.py::TestTradingEnvironment::test_get_portfolio_summary", "tests/test_rl_agent.py::TestTradingEnvironment::test_max_steps_termination", "tests/test_rl_agent.py::TestTradingEnvironment::test_position_switching", "tests/test_rl_agent.py::TestTradingEnvironment::test_reset", "tests/test_rl_agent.py::TestTradingEnvironment::test_step_long_action", "tests/test_rl_agent.py::TestTradingEnvironment::test_step_short_action", "tests/test_rl_agent.py::TestTradingEnvironment::test_step_wait_action", "tests/test_rl_agent.py::TestTradingRLAgent::test_agent_initialization", "tests/test_rl_agent.py::TestTradingRLAgent::test_agent_initialization_sac", "tests/test_rl_agent.py::TestTradingRLAgent::test_backtest", "tests/test_rl_agent.py::TestTradingRLAgent::test_evaluate_performance", "tests/test_rl_agent.py::TestTradingRLAgent::test_get_model_info", "tests/test_rl_agent.py::TestTradingRLAgent::test_predict_without_training", "tests/test_rl_agent.py::TestTradingRLAgent::test_save_and_load_model", "tests/test_rl_agent.py::TestTradingRLAgent::test_train_agent", "tests/test_symbol_scanner.py::TestSymbolMetrics::test_symbol_metrics_creation", "tests/test_symbol_scanner.py::TestSymbolScanner::test_add_remove_symbol", "tests/test_symbol_scanner.py::TestSymbolScanner::test_fetch_metrics_btc", "tests/test_symbol_scanner.py::TestSymbolScanner::test_fetch_metrics_doge", "tests/test_symbol_scanner.py::TestSymbolScanner::test_fetch_metrics_eth", "tests/test_symbol_scanner.py::TestSymbolScanner::test_fetch_metrics_invalid_symbol", "tests/test_symbol_scanner.py::TestSymbolScanner::test_find_best_all", "tests/test_symbol_scanner.py::TestSymbolScanner::test_find_best_multiple", "tests/test_symbol_scanner.py::TestSymbolScanner::test_find_best_single", "tests/test_symbol_scanner.py::TestSymbolScanner::test_get_all_metrics", "tests/test_symbol_scanner.py::TestSymbolScanner::test_get_scan_summary", "tests/test_symbol_scanner.py::TestSymbolScanner::test_get_symbol_metrics", "tests/test_symbol_scanner.py::TestSymbolScanner::test_rate_limiting", "tests/test_symbol_scanner.py::TestSymbolScanner::test_scanner_initialization", "tests/test_symbol_scanner.py::TestSymbolScanner::test_score_symbol", "tests/test_symbol_scanner.py::TestSymbolScanner::test_update_weights", "tests/test_symbol_scanner.py::TestSymbolScannerConfig::test_create_scanner", "tests/test_symbol_scanner.py::TestSymbolScannerConfig::test_default_symbols", "tests/test_symbol_scanner.py::TestSymbolScannerConfig::test_default_weights", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_invalid_action", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_json_with_extra_text", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_leverage_bounds_enforcement", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_malformed_json", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_missing_required_field", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_no_json_in_response", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_optional_fields_defaults", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_risk_pct_bounds_enforcement", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_valid_buy_instruction", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_valid_sell_instruction", "tests/test_trade_parsing.py::TestTradeInstructionParsing::test_valid_wait_instruction"]